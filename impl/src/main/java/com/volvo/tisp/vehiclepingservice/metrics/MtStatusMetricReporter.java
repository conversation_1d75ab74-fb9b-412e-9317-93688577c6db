package com.volvo.tisp.vehiclepingservice.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Metrics for MT status updates received from vehicles.
 */
@Component
public class MtStatusMetricReporter {
  static final String MT_STATUS = "mt-status";
  static final String TYPE = "TYPE";
  private final Counter canceledCounter;
  private final Counter deliveredCounter;
  private final Counter failedCounter;
  private final Counter overriddenCounter;
  private final Counter timeoutCounter;

  public MtStatusMetricReporter(MeterRegistry meterRegistry) {
    deliveredCounter = meterRegistry.counter(MT_STATUS, TYPE, "delivered");
    failedCounter = meterRegistry.counter(MT_STATUS, TYPE, "failed");
    timeoutCounter = meterRegistry.counter(MT_STATUS, TYPE, "timeout");
    canceledCounter = meterRegistry.counter(MT_STATUS, TYPE, "canceled");
    overriddenCounter = meterRegistry.counter(MT_STATUS, TYPE, "overridden");
  }

  public void onCanceled() {
    canceledCounter.increment();
  }

  public void onDelivered() {
    deliveredCounter.increment();
  }

  public void onFailed() {
    failedCounter.increment();
  }

  public void onOverridden() {
    overriddenCounter.increment();
  }

  public void onTimeout() {
    timeoutCounter.increment();
  }
}