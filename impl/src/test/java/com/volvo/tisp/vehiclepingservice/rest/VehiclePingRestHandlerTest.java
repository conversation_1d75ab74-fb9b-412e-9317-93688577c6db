package com.volvo.tisp.vehiclepingservice.rest;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;

import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.volvo.tisp.vehiclepingservice.converters.PingJsonOutputConverter;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.metrics.LegacyMetricsReporter;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;

@ExtendWith(SpringExtension.class)
public class VehiclePingRestHandlerTest {
  @Mock
  private PingJsonOutputConverter jsonOutputConverter;

  @Mock
  private LegacyMetricsReporter legacyMetricsReporter;

  @Mock
  private PingManager pingManager;

  @InjectMocks
  private VehiclePingRestHandler vehiclePingRestHandler;

  @Test
  void getDataFromCorrelationIdEmptyCorrelationIdTest() {
    PingEntity pingEntity = new PingEntity();
    Mockito.when(pingManager.getData("")).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(jsonOutputConverter.apply(pingEntity)).thenReturn("");

    CompletableFuture<String> result = vehiclePingRestHandler.getDataFromCorrelationId("");

    Assertions.assertNotNull(result);
    Assertions.assertEquals("", result.join());
    Mockito.verify(jsonOutputConverter).apply(pingEntity);
    Mockito.verify(pingManager).getData(Mockito.anyString());
    Mockito.verify(legacyMetricsReporter).legacyGetDataRequest();
    Mockito.verifyNoMoreInteractions(jsonOutputConverter, pingManager, legacyMetricsReporter);
  }

  @Test
  void getDataFromCorrelationIdReturnsExpectedCompletableFutureTest() {
    String correlationId = "testCorrelationId";
    String convertedData = "convertedData";
    PingEntity pingEntity = new PingEntity();
    Mockito.when(pingManager.getData(correlationId)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(jsonOutputConverter.apply(pingEntity)).thenReturn(convertedData);

    CompletableFuture<String> result = vehiclePingRestHandler.getDataFromCorrelationId(correlationId);

    Mockito.verify(legacyMetricsReporter).legacyGetDataRequest();
    Mockito.verify(pingManager).getData(correlationId);
    Mockito.verify(jsonOutputConverter).apply(pingEntity);

    Assertions.assertEquals(convertedData, result.join());
    Mockito.verifyNoMoreInteractions(jsonOutputConverter, pingManager, legacyMetricsReporter);
  }

  @Test
  void requestPingEmptyVpiTest() {
    String emptyVpi = "   ";
    RequestData expectedRequestData = new RequestData("", false, null, Channel.UDP, 30);

    Mockito.when(pingManager.pingRest(any(RequestData.class))).thenReturn(CompletableFuture.completedFuture("success"));

    CompletableFuture<String> result = vehiclePingRestHandler.requestPing(emptyVpi);

    Mockito.verify(legacyMetricsReporter).legacyPingRequest();
    Mockito.verify(pingManager).pingRest(eq(expectedRequestData));

    Assertions.assertNotNull(result);
    Assertions.assertEquals("success", result.join());
  }

  @Test
  void requestPingValidVpiTest() {
    String vpi = " ABC123 ";
    String expectedTrimmedVpi = "ABC123";
    RequestData expectedRequestData = new RequestData(expectedTrimmedVpi, false, null, Channel.UDP, 30);
    CompletableFuture<String> expectedResult = CompletableFuture.completedFuture("Ping successful");

    Mockito.when(pingManager.pingRest(any(RequestData.class))).thenReturn(expectedResult);

    CompletableFuture<String> result = vehiclePingRestHandler.requestPing(vpi);

    Mockito.verify(legacyMetricsReporter).legacyPingRequest();
    Mockito.verify(pingManager).pingRest(expectedRequestData);
    Assertions.assertEquals(expectedResult, result);
  }

  @Test
  void vehiclePingRestHandlerConstructorTest() {
    Assertions.assertNotNull(new VehiclePingRestHandler(jsonOutputConverter, pingManager, legacyMetricsReporter));
  }
}
