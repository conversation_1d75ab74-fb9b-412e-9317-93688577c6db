package com.volvo.tisp.vehiclepingservice.domain.v2.dto;

import java.util.Objects;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vps.api.TestService;

public class MoDtoV2 {

    private String correlationId;
    private TestService decoded;
    private String keyId;
    private long onBoardTimeStamp;
    private String receivingChannel;
    private long serverTimestamp;
    private String serviceAccessToken;
    private Vpi vpi;

    public String getCorrelationId() {
        return correlationId;
    }

    public TestService getDecoded() {
        return decoded;
    }

    public String getKeyId() {
        return keyId;
    }

    public long getOnBoardTimeStamp() {
        return onBoardTimeStamp;
    }

    public String getReceivingChannel() {
        return receivingChannel;
    }

    public long getServerTimestamp() {
        return serverTimestamp;
    }

    public String getServiceAccessToken() {
        return serviceAccessToken;
    }

    public Vpi getVpi() {
        return vpi;
    }

    public void setCorrelationId(String correlationId) {
        this.correlationId = correlationId;
    }

    public void setDecoded(TestService decoded) {
        this.decoded = decoded;
    }

    public void setKeyId(String keyId) {
        this.keyId = keyId;
    }

    public void setOnBoardTimeStamp(long onBoardTimeStamp) {
        this.onBoardTimeStamp = onBoardTimeStamp;
    }

    public void setReceivingChannel(String receivingChannel) {
        this.receivingChannel = receivingChannel;
    }

    public void setServerTimestamp(long serverTimestamp) {
        this.serverTimestamp = serverTimestamp;
    }

    public void setServiceAccessToken(String serviceAccessToken) {
        this.serviceAccessToken = serviceAccessToken;
    }

    public void setVpi(Vpi vpi) {
        this.vpi = vpi;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MoDtoV2 moDtoV2 = (MoDtoV2) o;
        return onBoardTimeStamp == moDtoV2.onBoardTimeStamp &&
                serverTimestamp == moDtoV2.serverTimestamp &&
                Objects.equals(correlationId, moDtoV2.correlationId) &&
                Objects.equals(decoded, moDtoV2.decoded) &&
                Objects.equals(keyId, moDtoV2.keyId) &&
                Objects.equals(receivingChannel, moDtoV2.receivingChannel) &&
                Objects.equals(serviceAccessToken, moDtoV2.serviceAccessToken) &&
                Objects.equals(vpi, moDtoV2.vpi);
    }

    @Override
    public int hashCode() {
        return Objects.hash(correlationId, decoded, keyId, onBoardTimeStamp, receivingChannel, serverTimestamp, serviceAccessToken, vpi);
    }

    @Override
    public String toString() {
        return "MoDtoV2{" +
                "correlationId='" + correlationId + '\'' +
                ", decoded=" + decoded +
                ", keyId='" + keyId + '\'' +
                ", onBoardTimeStamp=" + onBoardTimeStamp +
                ", receivingChannel='" + receivingChannel + '\'' +
                ", serverTimestamp=" + serverTimestamp +
                ", serviceAccessToken='" + serviceAccessToken + '\'' +
                ", vpi=" + vpi +
                '}';
    }
}

