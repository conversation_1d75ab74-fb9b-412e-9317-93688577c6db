package com.volvo.tisp.vehiclepingservice.integration.tests;

import org.springframework.stereotype.Component;
import org.springframework.test.web.reactive.server.WebTestClient;

@Component
public class HealthCheckTest {
  private final WebTestClient webTestClient;

  public HealthCheckTest(WebTestClient webTestClient) {
    this.webTestClient = webTestClient;
  }

  public void test() {
    webTestClient.get().uri("/actuator/health/").exchange().expectStatus().is2xxSuccessful();
    webTestClient.get().uri("/actuator/health/ping").exchange().expectStatus().is2xxSuccessful();
  }
}