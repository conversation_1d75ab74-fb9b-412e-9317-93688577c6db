package com.volvo.tisp.vehiclepingservice.domain.v1;

import java.time.Instant;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;

@ExtendWith(MockitoExtension.class)
class ReceivePongStepV1Test {

  @Mock
  private PingEntityService pingEntityService;
  @InjectMocks
  private ReceivePongStepV1 receivePongStepV1;
  @Mock
  private StateRepository stateRepository;

  private static PingEntity createMockPingEntity() {
    PingEntity pingEntity = new PingEntity();
    pingEntity.setCorrelationId(TestUtils.CORRELATION_ID);
    pingEntity.setVpi(TestUtils.VPI.toString());
    pingEntity.setStartTimeInMillis(Instant.now().toEpochMilli() - 5000); // 5 seconds ago
    return pingEntity;
  }

  @Test
  void actionShouldUpdatePingEntityWithCorrectValues() {
    PingEntity pingEntity = createMockPingEntity();

    Mockito.when(pingEntityService.findByMessageId(TestUtils.MESSAGE_ID)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(pingEntityService.upsert(Mockito.any(PingEntity.class))).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(stateRepository.pop(TestUtils.CORRELATION_ID)).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = receivePongStepV1.action(TestUtils.MESSAGE_ID, TestUtils.VPI);
    result.join();

    Assertions.assertEquals(Status.SUCCESS, pingEntity.getStatus());
    Assertions.assertNull(pingEntity.getPayloadReceived());
    Assertions.assertNotNull(pingEntity.getStopTimeInMillis());
    Assertions.assertTrue(pingEntity.getDuration() > 0);
  }

  @Test
  void actionWhenPingEntityExistsShouldProcessAndSave() {
    PingEntity pingEntity = createMockPingEntity();

    Mockito.when(pingEntityService.findByMessageId(TestUtils.MESSAGE_ID)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(pingEntityService.upsert(Mockito.any(PingEntity.class))).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(stateRepository.pop(TestUtils.CORRELATION_ID)).thenReturn(CompletableFuture.completedFuture(null));

    receivePongStepV1.action(TestUtils.MESSAGE_ID, TestUtils.VPI).join();

    Mockito.verify(pingEntityService).findByMessageId(TestUtils.MESSAGE_ID);
    Mockito.verify(pingEntityService).upsert(Mockito.any(PingEntity.class));
    Mockito.verify(stateRepository).pop(TestUtils.CORRELATION_ID);
  }

  @Test
  void actionWhenPingEntityNotFoundShouldReturnCompletedFuture() {
    Mockito.when(pingEntityService.findByMessageId(TestUtils.MESSAGE_ID)).thenReturn(CompletableFuture.completedFuture(null));

    receivePongStepV1.action(TestUtils.MESSAGE_ID, TestUtils.VPI).join();

    Mockito.verify(pingEntityService).findByMessageId(TestUtils.MESSAGE_ID);
    Mockito.verifyNoMoreInteractions(pingEntityService);
    Mockito.verifyNoInteractions(stateRepository);
  }

  @Test
  void actionWhenServiceThrowsExceptionShouldPropagateException() {
    Mockito.when(pingEntityService.findByMessageId(TestUtils.MESSAGE_ID))
        .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Service error")));

    CompletableFuture<Void> result = receivePongStepV1.action(TestUtils.MESSAGE_ID, TestUtils.VPI);

    Assertions.assertThrows(RuntimeException.class, result::join);
    Mockito.verify(pingEntityService).findByMessageId(TestUtils.MESSAGE_ID);
    Mockito.verifyNoMoreInteractions(pingEntityService);
    Mockito.verifyNoInteractions(stateRepository);
  }

  @Test
  void actionWhenUpsertFailsShouldPropagateException() {
    PingEntity pingEntity = createMockPingEntity();

    Mockito.when(pingEntityService.findByMessageId(TestUtils.MESSAGE_ID)).thenReturn(CompletableFuture.completedFuture(pingEntity));
    Mockito.when(pingEntityService.upsert(Mockito.any(PingEntity.class))).thenReturn(CompletableFuture.failedFuture(new RuntimeException("Upsert failed")));

    CompletableFuture<Void> result = receivePongStepV1.action(TestUtils.MESSAGE_ID, TestUtils.VPI);

    Assertions.assertThrows(RuntimeException.class, result::join);
    Mockito.verify(pingEntityService).findByMessageId(TestUtils.MESSAGE_ID);
    Mockito.verify(pingEntityService).upsert(Mockito.any(PingEntity.class));
    Mockito.verifyNoInteractions(stateRepository);
  }

}
