
//Synkron XMLHttpRequest på huvudtråden är föråldrad på grund av dess negativa inverkan på användarupplevelsen. För mer hjälp, se http://xhr.spec.whatwg.org/
const request = require("sync-request");

export class PermissionService {

  public isAuthenticatedForBeefy(): boolean {
    const res = request.default('GET', 'auth/beefy');
    return res.statusCode === 200 || res.statusCode === 204;
  }

  public isAuthenticatedForCustom(): boolean {
    const res = request.default('GET', 'auth/custom');
    return res.statusCode === 200 || res.statusCode === 204;
  }
}
