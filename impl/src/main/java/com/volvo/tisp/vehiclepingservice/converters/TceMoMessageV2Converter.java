package com.volvo.tisp.vehiclepingservice.converters;

import java.util.Optional;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vehiclepingservice.domain.v2.PingV2Coder;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vps.api.TestService;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.Property;

@Component
public class TceMoMessageV2Converter {
  private static final String RECEIVING_CHANNEL = "TransportType";

  private final PingV2Coder pingV2Coder;

  public TceMoMessageV2Converter(PingV2Coder pingV2Coder) {
    this.pingV2Coder = pingV2Coder;
  }

  private static MoDtoV2 createMoDtoV2(MoMessage moMessage, TestService decoded) {
    MoDtoV2 dto = new MoDtoV2();

    dto.setVpi(Vpi.ofString(moMessage.getVehiclePlatformId()));
    dto.setDecoded(decoded);
    return dto;
  }

  private static Optional<String> getReceivingChannel(MoMessage moMessage) {
    return moMessage.getSolutionSpecificProperties().stream()
        .filter(p -> p.getKey().equalsIgnoreCase(RECEIVING_CHANNEL))
        .map(Property::getValue)
        .findFirst();
  }

  public MoDtoV2 convert(MoMessage moMessage) {
    Validate.notNull(moMessage, "moMessage");

    TestService decoded = pingV2Coder.decode(moMessage.getPayload());
    MoDtoV2 dto = createMoDtoV2(moMessage, decoded);

    Optional<String> receivingChannel = getReceivingChannel(moMessage);
    // possible values are: UDP,SMS,SATELLITE
    receivingChannel.ifPresent(dto::setReceivingChannel);

    return dto;
  }
}
