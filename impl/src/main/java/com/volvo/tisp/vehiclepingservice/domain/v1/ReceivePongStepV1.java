package com.volvo.tisp.vehiclepingservice.domain.v1;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;

@Component
public class ReceivePongStepV1 extends ReceiveStep {
  private static final Logger logger = LoggerFactory.getLogger(ReceivePongStepV1.class);

  public ReceivePongStepV1(PingEntityService pingEntityService, StateRepository stateRepository) {
    super(pingEntityService, stateRepository);
  }

  public CompletableFuture<Void> action(long swapCorrelationId, Vpi vpi) {
    return pingEntityService.findByMessageId(swapCorrelationId)
        .thenCompose(
            pingEntity -> {
              if (pingEntity == null) {
                logger.warn("Unknown V1 Pong: {}, vpi: {}", swapCorrelationId, vpi);
                return CompletableFuture.completedFuture(null);
              }

              return validateAndSave(pingEntity, null).thenAccept(
                  savedPingEntity -> logger.info("Received Pong V1 after {} seconds for vpi: {}", savedPingEntity.getDuration(), pingEntity.getVpi()));
            });
  }
}
