package com.volvo.tisp.vehiclepingservice.database.repository;

import static org.springframework.data.mongodb.core.FindAndModifyOptions.options;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import java.util.concurrent.CompletableFuture;

import org.springframework.data.mongodb.core.ReactiveMongoOperations;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vehiclepingservice.database.entity.DatabaseSequence;

import reactor.core.publisher.Mono;

@Component
public class MessageIdRepo {

  private static final String MESSAGE_ID_SEQUENCE_NAME = "messageIdSequence";

  private final ReactiveMongoOperations mongoOperations;

  public MessageIdRepo(ReactiveMongoOperations mongoOperations) {
    this.mongoOperations = mongoOperations;
  }

  public CompletableFuture<Long> getNextMessageIdSequence() {
    return getNextSequence(MESSAGE_ID_SEQUENCE_NAME)
        .map(DatabaseSequence::messageIdSequence)
        .switchIfEmpty(Mono.just(1L))
        .toFuture();
  }

  private Mono<DatabaseSequence> getNextSequence(String sequenceName) {
    return mongoOperations.findAndModify(
        query(where("_id").is(sequenceName)),
        new Update().inc(MESSAGE_ID_SEQUENCE_NAME, 1),
        options().returnNew(true).upsert(true),
        DatabaseSequence.class);
  }
}
