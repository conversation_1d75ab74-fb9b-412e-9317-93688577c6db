package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class ServiceAccessTokenMetricReporterTest {
  @Test
  void onRetrievingNewToken() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onRetrievingNewToken();
      MetricsReporterTestUtils.checkCounter(registry, 1, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of("bostoken", "new"));
    });
  }

  @Test
  void onReuseToken() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onReuseToken();
      MetricsReporterTestUtils.checkCounter(registry, 1, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of("bostoken", "reuse"));
    });
  }

  @Test
  void onTokenFailure() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onTokenFailure();
      MetricsReporterTestUtils.checkCounter(registry, 1, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of(ServiceAccessTokenMetricReporter.TYPE, "failure"));
    });
  }

  @Test
  void onTokenRequest() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onTokenRequest();
      MetricsReporterTestUtils.checkCounter(registry, 1, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of(ServiceAccessTokenMetricReporter.TYPE, "request"));
    });
  }

  @Test
  void onTokenSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onTokenSuccess();
      MetricsReporterTestUtils.checkCounter(registry, 1, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of(ServiceAccessTokenMetricReporter.TYPE, "success"));
    });
  }

  @Test
  void onValidateFailure() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onValidateFailure();
      MetricsReporterTestUtils.checkCounter(registry, 1, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of(ServiceAccessTokenMetricReporter.TYPE, "validate-failure"));
    });
  }

  @Test
  void onValidateSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(ServiceAccessTokenMetricReporter::new, (registry, reporter) -> {
      reporter.onValidateSuccess();
      MetricsReporterTestUtils.checkCounter(registry, 1, ServiceAccessTokenMetricReporter.SERVICE_ACCESS_TOKEN,
          Tags.of(ServiceAccessTokenMetricReporter.TYPE, "validate-success"));
    });
  }
}