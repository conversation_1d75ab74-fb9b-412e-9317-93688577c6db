package com.volvo.tisp.vehiclepingservice.integration.tests.mock;

import java.io.StringReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.UUID;

import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;

import org.junit.jupiter.api.Assertions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.test.jms.QueueConsumer;
import com.volvo.tisp.framework.test.jms.TestJmsClient;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.domain.v1.PingCoder;
import com.volvo.tisp.vehiclepingservice.domain.v2.PingV2Coder;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.jms.MoMessageJmsHandler;
import com.volvo.tisp.vehiclepingservice.jms.MtStatusJmsHandler;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessage;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessageResponse;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Ping;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Pong;
import com.volvo.tisp.vehiclepingservice.util.ChecksumCalculator;
import com.volvo.tisp.vps.api.PingRequest;
import com.volvo.tisp.vps.api.PingResponse;
import com.volvo.tisp.vps.api.PingResponse.Status;
import com.volvo.tisp.vps.api.TestService;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

public class TceMock {
  public static final String MO_MESSAGE_IN_QUEUE = TestUtil.QUEUE_PREFIX + MoMessageJmsHandler.MO_MESSAGE_IN_QUEUE;
  public static final String MT_STATUS_IN_QUEUE = TestUtil.QUEUE_PREFIX + MtStatusJmsHandler.MT_STATUS_IN_QUEUE;
  public static String TCE_MT_QUEUE = TestUtil.QUEUE_PREFIX + "MT.IN";
  private static final Logger logger = LoggerFactory.getLogger(TceMock.class);
  private final TestJmsClient jmsClient;
  private final PingCoder pingCoder;
  private final PingV2Coder pingCoderV2;
  private final QueueConsumer queueConsumer;
  private final Unmarshaller unmarshaller;

  public TceMock(TestJmsClient jmsClient, PingCoder pingCoder, PingV2Coder pingCoderV2, QueueConsumer queueConsumer) {
    this.jmsClient = jmsClient;
    this.pingCoder = pingCoder;
    this.pingCoderV2 = pingCoderV2;
    this.queueConsumer = queueConsumer;

    try {
      JAXBContext jc = JAXBContext.newInstance(MtMessage.class);
      this.unmarshaller = jc.createUnmarshaller();
    } catch (JAXBException e) {
      throw new RuntimeException(e);
    }
  }

  private static MoDtoV2 createMoDtoV2(MtMessage mtMessage, TestService decoded) {
    MoDtoV2 dto = new MoDtoV2();
    dto.setDecoded(decoded);
    dto.setVpi(Vpi.ofString(mtMessage.getVehiclePlatformId()));
    return dto;
  }

  private static void verifyChannel(Channel channel, String sendSchema) {
    switch (channel) {
      case UDP -> Assertions.assertEquals("dfol-mid-plus", sendSchema);
      case SAT -> Assertions.assertEquals("sat-only", sendSchema);
      case SMS -> Assertions.assertEquals("sms-only", sendSchema);
    }
  }

  public void checkAndReplyBeefy(String payloadToRespond) {
    logger.info("Waiting for BeefyMessage (tce-format) -> vehicle...");
    MtMessage mtMessage = receiveTceMtMessage();
    String correlationId = mtMessage.getMtStatusReplyOption().getCorrelationId();
    logger.info("\"Vehicle\" Received BeefyMessage with correlationId='{}'", correlationId);

    MtStatusMessage mtStatusMessage = TestUtil.createMtStatusMessage(correlationId, mtMessage.getVehiclePlatformId(), "DELIVERED");
    sendMtStatusJmsMessage(mtStatusMessage, correlationId);

    BeefyMessage beefyMessage = pingCoder.decode(mtMessage.getPayload()).getBeefyMessage();
    byte[] responsePayload = null;
    if (payloadToRespond == null) {
      pingCoder.encodeBeefyResponse(beefyMessage.getId(), beefyMessage.getPayload());
    } else {
      responsePayload = pingCoder.encodeBeefyResponse(beefyMessage.getId(), payloadToRespond.getBytes(Charset.defaultCharset()));
    }

    MoMessage moMessage = TestUtil.createMoMessage(responsePayload, 1, mtMessage.getVehiclePlatformId());
    sendMoJmsMessage(moMessage);
  }

  public void checkAndReplyPong() {
    Message msg = receiveRawTceMtMessage();
    replyMtStatus(msg, "DELIVERED");
    replyPong(msg);
  }

  public void checkAndReplyPongV2() {
    checkAndReplyPongV2(Channel.UDP, true);
  }

  public void checkAndReplyPongV2(Channel channel, boolean validChecksum) {
    MtMessage mtMessage = receiveTceMtMessage();
    // Legacy SendSchema check
    String sendSchema = mtMessage.getSchedulerOption().getHint();

    verifyChannel(channel, sendSchema);

    replyMtStatus(mtMessage, "DELIVERED");
    replyPongV2(mtMessage, validChecksum);
  }

  public void checkBeefyMessageResponse(long id, String payload, boolean copyPayload) {
    logger.info("Waiting for pong (tce-format) -> vehicle...");
    MtMessage mtMessage = receiveTceMtMessage();
    logger.info("Received PING for tceMock");

    Assertions.assertEquals(256, mtMessage.getSrpOption().getDstService());
    Assertions.assertEquals(1, mtMessage.getSrpOption().getDstVersion());

    BeefyMessageResponse resp = pingCoder.decode(mtMessage.getPayload()).getBeefyMessageResponse();

    Assertions.assertNotNull(resp);
    Assertions.assertEquals(id, resp.getId());
    if (copyPayload) {
      Assertions.assertEquals(id, resp.getId());
      Assertions.assertArrayEquals(payload.getBytes(StandardCharsets.UTF_8), resp.getPayload());
    } else {
      Assertions.assertArrayEquals(new byte[] {}, resp.getPayload());
    }
  }

  public void clear() {
    logger.info("Clear TCE-mock JMS queue");
    try {
      queueConsumer.purgeQueue(TCE_MT_QUEUE);
    } catch (JMSException e) {
      throw new RuntimeException(e);
    }
  }

  public Message receiveRawTceMtMessage() {
    logger.info("Waiting for ping (tce-format) -> vehicle...");
    try {
      Message message = queueConsumer.waitForMessage(TCE_MT_QUEUE, Duration.ofSeconds(15));

      Assertions.assertNotNull(message, "Received message should not be null");

      return message;
    } catch (JMSException e) {
      throw new RuntimeException(e);
    }
  }

  public MtMessage receiveTceMtMessage() {
    Message msg = receiveRawTceMtMessage();
    MtMessage mtMessage;

    try {
      String body = msg.getBody(String.class);
      mtMessage = (MtMessage) unmarshaller.unmarshal(new StringReader(body));
      return mtMessage;
    } catch (JMSException | JAXBException e) {
      throw new RuntimeException(e);
    }
  }

  public void replyMtStatus(Message msg, String status) {
    String correlationId;
    String vpi;
    try {
      String txt = msg.getBody(String.class);
      MtMessage body = (MtMessage) unmarshaller.unmarshal(new StringReader(txt));
      correlationId = body.getMtStatusReplyOption().getCorrelationId();
      vpi = body.getVehiclePlatformId();
    } catch (JMSException | JAXBException e) {
      throw new RuntimeException(e);
    }
    logger.info("Received PING with correlationId='{}'", correlationId);
    replyMtStatus(correlationId, vpi, status);
  }

  public void replyMtStatus(MtMessage mtMessage, String status) {
    String correlationId = mtMessage.getMtStatusReplyOption().getCorrelationId();
    String vpi = mtMessage.getVehiclePlatformId();
    logger.info("Received PING with correlationId='{}'", correlationId);
    replyMtStatus(correlationId, vpi, status);
  }

  public void replyMtStatus(String correlationId, String vpi, String status) {
    MtStatusMessage mtStatusMessage = new MtStatusMessage();
    mtStatusMessage.setCorrelationId(correlationId);
    mtStatusMessage.setVehiclePlatformId(vpi);
    mtStatusMessage.setStatus(status);
    sendMtStatusJmsMessage(mtStatusMessage, correlationId);
  }

  public void replyPong(Message oneWithMessage) {
    MtMessage tceMtMessage;
    try {
      String body = oneWithMessage.getBody(String.class);
      tceMtMessage = (MtMessage) unmarshaller.unmarshal(new StringReader(body));
    } catch (JMSException | JAXBException e) {
      throw new RuntimeException(e);
    }
    com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1 decoded = pingCoder.decode(tceMtMessage.getPayload());
    if (decoded.getPing() != null) {
      Ping ping = decoded.getPing();
      if (ping.getResponseExpected()) {
        byte[] encodedPong = pingCoder.encodePong(ping.getId());
        MoMessage moMessage = TestUtil.createMoMessage(encodedPong, 1, tceMtMessage.getVehiclePlatformId());
        logger.info("Sending MoResponse (Pong) dstService(256) with messageId='{}', vpi='{}'", ping.getId(), moMessage.getVehiclePlatformId());
        sendMoJmsMessage(moMessage);
      }
    } else if (decoded.getBeefyMessage() == null) {
      BeefyMessage beefy = decoded.getBeefyMessage();
      if (beefy.getResponseExpected()) {
        byte[] encodedPong = pingCoder.encodeBeefyResponse(beefy.getId(), beefy.getPayload());
        MoMessage moMessage = TestUtil.createMoMessage(encodedPong, 1, tceMtMessage.getVehiclePlatformId());
        logger.info("Sending MoResponse (Beefy) dstService(256) with messageId='{}', vpi='{}'", beefy.getId(), moMessage.getVehiclePlatformId());
        sendMoJmsMessage(moMessage);
      }
    }
  }

  public long sendBeefy(String payload, boolean copyPayload) {
    String vpi = TestUtil.createVehiclePlatformIdentifier();
    TestUtil.stubTus(vpi, 1);
    long id = randomMessageId();
    MoMessage moMessage = TestUtil.createMoMessage(pingCoder.encodeBeefyPing(id, copyPayload, payload), 1, vpi);
    sendMoJmsMessage(moMessage);
    return id;
  }

  public void sendMoJmsMessage(MoMessage moMessage) {
    TispContext.runInContext(() ->
        jmsClient
            .newMessage()
            .payload(moMessage)
            .messageType(MessageTypesJms.TCE_MO_MESSAGE_TYPE)
            .messageVersion(MessageTypesJms.VERSION_2_0)
            .sendTo(MO_MESSAGE_IN_QUEUE)
    );
  }

  public void sendMoMessage(String vehiclePlatformId, byte[] encodedPong, int version) {
    MoMessage moMessage = TestUtil.createMoMessage(encodedPong, version, vehiclePlatformId);
    logger.info("Sending TCE-MoResponse (Pong) dstService(256), vpi='{}'", vehiclePlatformId);
    sendMoJmsMessage(moMessage);
  }

  public void sendMtStatusJmsMessage(MtStatusMessage mtStatus, String correlationId) {
    TispContext.runInContext(() ->
        jmsClient
            .newMessage()
            .correlationId(correlationId)
            .payload(mtStatus)
            .messageType(MessageTypesJms.TCE_MTSTATUS_MESSAGE_TYPE)
            .messageVersion(MessageTypesJms.VERSION_2_0)
            .sendTo(MT_STATUS_IN_QUEUE)
    );
    logger.info("Send mt status, corrId='{}', vpi={}, status={}", correlationId, mtStatus.getVehiclePlatformId(), mtStatus.getStatus());
  }

  public long sendPingV1() {
    String vpi = TestUtil.createVehiclePlatformIdentifier();
    TestUtil.stubTus(vpi, 1);
    long id = randomMessageId();

    MoMessage moMessage = TestUtil.createMoMessage(pingCoder.encodePing(id), 1, vpi);
    sendMoJmsMessage(moMessage);

    return id;
  }

  public UUID sendPingV2(String payload, boolean copyPayload) {
    String vpi = TestUtil.createVehiclePlatformIdentifier();
    UUID uuid = UUID.randomUUID();
    TestUtil.stubTus(vpi, 2);

    MoMessage moMessage = TestUtil.createMoMessage(pingCoderV2.encodePing(uuid, payload, copyPayload), 2, vpi);
    sendMoJmsMessage(moMessage);

    return uuid;
  }

  public void verifyPongV1(long id) {
    logger.info("Waiting for pong (tce-format) -> vehicle...");
    MtMessage mtMessage = receiveTceMtMessage();

    replyMtStatus(mtMessage.getMtStatusReplyOption().getCorrelationId(), mtMessage.getVehiclePlatformId(), "DELIVERED");
    logger.info("Received PING for tceMock");
    Pong pong = pingCoder.decode(mtMessage.getPayload()).getPong();

    Assertions.assertNotNull(pong);
    Assertions.assertEquals(id, pong.getId());
  }

  public void verifyPongV2ToVehicle(UUID uuid) {
    MtMessage mtMessage = receiveTceMtMessage();
    logger.info("Received PING for tceMock");

    Assertions.assertNotNull(mtMessage);
    Assertions.assertEquals(256, mtMessage.getSrpOption().getDstService());
    Assertions.assertEquals(2, mtMessage.getSrpOption().getDstVersion());

    byte[] pingPayload = mtMessage.getPayload();
    TestService decoded = pingCoderV2.decode(pingPayload);
    PingResponse pingResponse = decoded.getPingResponse();

    Assertions.assertNotNull(pingResponse);
    Assertions.assertEquals(uuid, pingResponse.getCorrelationId());
    Assertions.assertEquals(Status.OK, pingResponse.getStatus());
  }

  private long randomMessageId() {
    long temp = -1L;
    // 4294967295 is the max value for it in SWAP
    while (temp < 0 || temp > 4294967295L) {
      temp = (long) Math.abs((Math.random() * 4294967295L) - 1L);
    }
    return temp;
  }

  private void replyPongV2(MtMessage mtMessage, boolean validChecksum) {
    TestService decoded = pingCoderV2.decode(mtMessage.getPayload());

    if (decoded.getPingRequest() != null) {
      PingRequest ping = decoded.getPingRequest();

      if (!validChecksum) {
        ping.getPayload().setChecksum(ChecksumCalculator.getSHA256Hash("RanomPayload"));
      }

      MoDtoV2 dto = createMoDtoV2(mtMessage, decoded);
      byte[] newPayload = pingCoderV2.encodePong(dto, null);
      MoMessage moMessage = TestUtil.createMoMessage(newPayload, 2, mtMessage.getVehiclePlatformId());
      logger.info("Sending TCE-MoResponse (Pong) dstService(256.2) with correlationID='{}', vpi='{}'", ping.getCorrelationId(), dto.getVpi());

      sendMoJmsMessage(moMessage);
    }
  }
}
