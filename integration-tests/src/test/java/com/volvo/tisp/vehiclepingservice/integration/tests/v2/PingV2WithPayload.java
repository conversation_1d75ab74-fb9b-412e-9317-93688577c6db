package com.volvo.tisp.vehiclepingservice.integration.tests.v2;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.IdpMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.Idpm2mMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.JWKSClientTestMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.MtDispatcherMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.StateRepositoryRestMockExtended;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.TceMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.RestUtil;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.model.PingToVehicleRequest;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;
import com.volvo.tisp.vps.api.PingResponse;


@Component
public class PingV2WithPayload {
  private final JWKSClientTestMock jWKSClientTestMock;
  private final MtDispatcherMock mtDispatcherMock;
  private final MtMessagePublisher mtMessagePublisher;
  private final PingEntityService pingEntityService;
  private final ServiceAccessTokenService serviceAccessTokenService;
  private final StateRepositoryRestMockExtended stateRepositoryRestMock;
  private final TceMock tceMock;
  private final WebTestClient webTestClient;

  public PingV2WithPayload(JWKSClientTestMock jWKSClientTestMock, MtDispatcherMock mtDispatcherMock, MtMessagePublisher mtMessagePublisher,
      PingEntityService pingEntityService, ServiceAccessTokenService serviceAccessTokenService, StateRepositoryRestMockExtended stateRepositoryRestMock,
      TceMock tceMock, WebTestClient webTestClient) {
    this.jWKSClientTestMock = jWKSClientTestMock;
    this.mtDispatcherMock = mtDispatcherMock;
    this.mtMessagePublisher = mtMessagePublisher;
    this.pingEntityService = pingEntityService;
    this.serviceAccessTokenService = serviceAccessTokenService;
    this.stateRepositoryRestMock = stateRepositoryRestMock;
    this.tceMock = tceMock;
    this.webTestClient = webTestClient;
  }

  public void test(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    mtMessagePublisher.setMtDispatcherFeature(mtDispatcher);
    serviceAccessTokenService.setServiceAccessTokenValidationEnabled(tokenValidation);

    if (mtDispatcher) {
      Idpm2mMock.mockCallToFetchServiceAccessToken("mh.w svc.256", "clientId");
    }
    String obsToken = null;
    if (tokenValidation) {
      obsToken = jWKSClientTestMock.setUpJwtToken();
    }

    String vpi = TestUtil.stubTusSubscription(2);

    stateRepositoryRestMock.stubPush(Instant.now().plus(3, ChronoUnit.MINUTES), 200);

    PingToVehicleRequest pingToVehicleRequest = TestUtil.createPingToVehicleRequestUdp(vpi, false, TestUtil.PAYLOAD);
    // REST api call
    String correlationId = String.valueOf(
        RestUtil.verifyPostHttpResponse(webTestClient, "/v2/ping", obsToken, pingToVehicleRequest, PingResponse.class)
            .getCorrelationId());

    PingEntity pingEntity = pingEntityService.findByCorrelationId(correlationId).get();
    TestUtil.verifyPingEntityPending(vpi, pingEntity);

    stateRepositoryRestMock.stubPop(correlationId, List.of(correlationId));
    // Respond with Pong
    if (mtDispatcher) {
      mtDispatcherMock.checkAndReplyPongV2(useMoRouter, obsToken);
    } else {
      tceMock.checkAndReplyPongV2();
    }

    // Poll DB data
    Awaitility.await()
        .atMost(Duration.of(10, ChronoUnit.SECONDS))
        .until(() -> pingEntityService.findByCorrelationId(correlationId).join().getStatus() != Status.PENDING);
    PingEntity pingEntityFinalDb = pingEntityService.findByCorrelationId(correlationId).join();

    // assert DB data
    TestUtil.verifyPingEntitySuccessWithPayload(pingEntityFinalDb, vpi);

    if (mtDispatcher) {
      IdpMock.verifyIdpWiremockTokenInteractions(1);
    }
  }
}