package com.volvo.tisp.vehiclepingservice.jms;

import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.subscriptionrepository.client.DefaultMessagePublisher;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

public class TceMessagePublisherTest {
  private MessagePublisher.Message<MtDto> mockMessage;
  private TceMessagePublisher tceMessagePublisher;

  private static MtDto createMtDto() {
    MtDto dto = new MtDto();
    dto.setVpi(Vpi.ofString("1234567890ABCDEF1234567890ABCDEF"));
    dto.setServiceVersion(1);
    dto.setCorrelationId("testCorrelationId");
    dto.setPayload("testPayload".getBytes());
    dto.setServiceAccessToken("testToken");
    dto.setChannel(Channel.UDP);
    dto.setTimeout(300);
    dto.setSystem("testSystem");
    return dto;

  }

  @Test
  void convertCreatesCorrectMtMessageTest() {
    MtDto dto = createMtDto();

    TispContext.runInContext(() -> {
      MtMessage mtMessage = tceMessagePublisher.convert(dto);
      Assertions.assertEquals("1234567890ABCDEF1234567890ABCDEF", mtMessage.getVehiclePlatformId());
      Assertions.assertEquals("testPayload", new String(mtMessage.getPayload()));
      Assertions.assertEquals("testCorrelationId", mtMessage.getMtStatusReplyOption().getCorrelationId());

      Assertions.assertEquals("dfol-mid-plus", mtMessage.getSchedulerOption().getHint());
      Assertions.assertEquals(256, mtMessage.getSrpOption().getDstService());
      Assertions.assertEquals(1, mtMessage.getSrpOption().getDstVersion());

      Assertions.assertNull(mtMessage.getSrpOption().getSrpLevel());
    });
  }

  @Test
  void publishMessageWithOptionsTest() {
    MtDto dto = createMtDto();
    Mockito.when(mockMessage.option(Mockito.anyString(), Mockito.anyString())).thenReturn(mockMessage);
    Mockito.when(mockMessage.publish(dto)).thenReturn(CompletableFuture.completedFuture(1));

    CompletableFuture<Integer> result = tceMessagePublisher.publish(dto);

    Mockito.verify(mockMessage).option(TceMessagePublisher.SUBSCRIPTION_OPTION_TCE_SYSTEM, dto.getSystem());
    Mockito.verify(mockMessage).option(TceMessagePublisher.SUBSCRIPTION_OPTION_SRP_DST_SERVICE, TceMessagePublisher.SERVICE_ID);
    Mockito.verify(mockMessage).publish(dto);
    Assertions.assertEquals(1, result.join());
  }

  @Test
  void publishWithNullSystemTest() {
    MtDto dto = createMtDto();
    dto.setSystem(null);

    Mockito.when(mockMessage.option(TceMessagePublisher.SUBSCRIPTION_OPTION_TCE_SYSTEM, (String) null)).thenReturn(mockMessage);
    Mockito.when(mockMessage.option(TceMessagePublisher.SUBSCRIPTION_OPTION_SRP_DST_SERVICE, TceMessagePublisher.SERVICE_ID)).thenReturn(mockMessage);
    Mockito.when(mockMessage.publish(dto)).thenReturn(CompletableFuture.completedFuture(1));

    CompletableFuture<Integer> result = tceMessagePublisher.publish(dto);

    Assertions.assertNotNull(result);
    Assertions.assertEquals(1, result.join());

    Mockito.verify(mockMessage).option(TceMessagePublisher.SUBSCRIPTION_OPTION_TCE_SYSTEM, (String) null);
    Mockito.verify(mockMessage).option(TceMessagePublisher.SUBSCRIPTION_OPTION_SRP_DST_SERVICE, TceMessagePublisher.SERVICE_ID);
    Mockito.verify(mockMessage).publish(dto);
  }

  @BeforeEach
  void setUp() {
    MessagePublisher<MtDto> messagePublisher = Mockito.mock(MessagePublisher.class);
    mockMessage = Mockito.mock(MessagePublisher.Message.class);

    DefaultMessagePublisher.Builder messagePublisherBuilder = Mockito.mock(DefaultMessagePublisher.Builder.class);
    MessagePublisher.Builder.Step2<MtDto> messagePublisherBuilderStep2 = Mockito.mock(DefaultMessagePublisher.Builder.Step2.class);
    MessagePublisher.Builder.Step3<MtDto> messagePublisherBuilderStep3 = Mockito.mock(DefaultMessagePublisher.Builder.Step3.class);

    Mockito.when(messagePublisherBuilder.messageType(MessageTypesJms.TCE_MT_MESSAGE_TYPE, MtDto.class)).thenReturn(messagePublisherBuilderStep2);
    Mockito.when(messagePublisherBuilderStep2.version(Mockito.eq(MessageTypesJms.VERSION_2_0), Mockito.any()))
        .thenReturn(messagePublisherBuilderStep3);
    Mockito.when(messagePublisherBuilderStep3.build()).thenReturn(messagePublisher);
    Mockito.when(messagePublisher.newMessage()).thenReturn(mockMessage);

    tceMessagePublisher = new TceMessagePublisher(messagePublisherBuilder);
  }
}
