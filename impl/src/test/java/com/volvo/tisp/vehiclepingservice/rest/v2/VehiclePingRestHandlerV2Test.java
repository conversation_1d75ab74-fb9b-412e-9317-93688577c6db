package com.volvo.tisp.vehiclepingservice.rest.v2;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.server.ResponseStatusException;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.vehiclepingservice.converters.VehiclePingOutputConverter;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.metrics.VehiclePingRestMetricReporter;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.model.PingToVehicleRequest;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingDataResponse;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingResponse;
import com.volvo.tisp.vehiclepingservice.util.TimeoutCalculator;

@ExtendWith(SpringExtension.class)
public class VehiclePingRestHandlerV2Test {
  private static final String VPI = "1234567890ABCDEF1234567890ABCDEF";
  @Mock
  private PingManager pingManager;
  @Mock
  private TimeoutCalculator timeoutCalculator;
  @Mock
  private VehiclePingOutputConverter vehiclePingOutputConverter;
  @InjectMocks
  private VehiclePingRestHandlerV2 vehiclePingRestHandlerV2;
  @Mock
  private VehiclePingRestMetricReporter vehiclePingRestMetricReporter;

  @Test
  void constructorTest() {
    Assertions.assertNotNull(new VehiclePingRestHandlerV2(pingManager, vehiclePingOutputConverter, timeoutCalculator, vehiclePingRestMetricReporter));
  }

  @Test
  void getDataNotFoundTest() {
    UUID correlationId = UUID.randomUUID();

    Mockito.when(pingManager.getData(correlationId.toString()))
        .thenReturn(CompletableFuture.failedFuture(
            new java.util.concurrent.CompletionException(new com.volvo.tisp.vehiclepingservice.domain.exceptions.NotFoundException("Data not found")))
        );

    CompletableFuture<ResponseEntity<VehiclePingDataResponse>> result = vehiclePingRestHandlerV2.getData(correlationId);

    ResponseEntity<VehiclePingDataResponse> response = result.join();
    Assertions.assertEquals(404, response.getStatusCodeValue());
    Assertions.assertNull(response.getBody());
    Mockito.verify(pingManager).getData(correlationId.toString());
    Mockito.verify(vehiclePingRestMetricReporter).onDataRequest();
    Mockito.verify(vehiclePingRestMetricReporter).onDataNotFound();

    Mockito.verifyNoMoreInteractions(pingManager, vehiclePingOutputConverter, vehiclePingRestMetricReporter, timeoutCalculator);
  }

  @Test
  void getDataSuccessfulRetrievalTest() {
    UUID correlationId = UUID.randomUUID();

    PingEntity rawData = new PingEntity();
    VehiclePingDataResponse convertedData = new VehiclePingDataResponse();
    Mockito.when(pingManager.getData(correlationId.toString())).thenReturn(CompletableFuture.completedFuture(rawData));
    Mockito.when(vehiclePingOutputConverter.convert(rawData)).thenReturn(convertedData);

    CompletableFuture<ResponseEntity<VehiclePingDataResponse>> result = vehiclePingRestHandlerV2.getData(correlationId);

    Assertions.assertNotNull(result);
    ResponseEntity<VehiclePingDataResponse> responseEntity = result.join();
    Assertions.assertEquals(200, responseEntity.getStatusCodeValue());
    Assertions.assertEquals(convertedData, responseEntity.getBody());
    Mockito.verify(pingManager).getData(correlationId.toString());
    Mockito.verify(vehiclePingRestMetricReporter).onDataRequest();
    Mockito.verify(vehiclePingRestMetricReporter).onDataSuccess();
    Mockito.verify(vehiclePingOutputConverter).convert(Mockito.any(PingEntity.class));
    Mockito.verifyNoMoreInteractions(pingManager, vehiclePingOutputConverter, vehiclePingRestMetricReporter, timeoutCalculator);
  }

  @Test
  void getDataUnexpectedExceptionTest() {
    UUID correlationId = UUID.randomUUID();
    Mockito.when(pingManager.getData(correlationId.toString())).thenReturn(CompletableFuture.failedFuture(new RuntimeException("Unexpected error")));

    CompletableFuture<ResponseEntity<VehiclePingDataResponse>> result = vehiclePingRestHandlerV2.getData(correlationId);

    ResponseEntity<VehiclePingDataResponse> response = result.join();
    Assertions.assertEquals(500, response.getStatusCodeValue());
    Assertions.assertNull(response.getBody());
    Mockito.verify(pingManager).getData(correlationId.toString());
    Mockito.verify(vehiclePingRestMetricReporter).onDataRequest();
    Mockito.verify(vehiclePingRestMetricReporter).onDataFailure();
    Mockito.verifyNoMoreInteractions(pingManager, vehiclePingOutputConverter, vehiclePingRestMetricReporter, timeoutCalculator);
  }

  @Test
  void pingToVehicleSuccessfulPingTest() {
    PingToVehicleRequest request = new PingToVehicleRequest();
    request.setVehiclePlatformId(VPI);
    request.setChannel(Channel.UDP);
    request.setCopyPayload(false);
    request.setPayload("Test payload");

    String correlationId = UUID.randomUUID().toString();

    Mockito.when(timeoutCalculator.getDefaultTimeout(any())).thenReturn(30);
    Mockito.when(pingManager.pingRest(any())).thenReturn(CompletableFuture.completedFuture(correlationId));

    TispContext.runInContext(() -> {
      CompletableFuture<ResponseEntity<VehiclePingResponse>> result = vehiclePingRestHandlerV2.pingToVehicle(request);
      Assertions.assertTrue(result.isDone());

      try {
        ResponseEntity<VehiclePingResponse> response = result.get();
        Assertions.assertNotNull(response);
        Assertions.assertEquals(200, response.getStatusCodeValue());
        Assertions.assertNotNull(response.getBody());
        Assertions.assertEquals(UUID.fromString(correlationId), response.getBody().getCorrelationId());
      } catch (InterruptedException | ExecutionException e) {
        throw new RuntimeException(e);
      }
    });

    Mockito.verify(vehiclePingRestMetricReporter).onPingRequest(Channel.UDP);
    Mockito.verify(vehiclePingRestMetricReporter).onPingSuccess();
    Mockito.verify(pingManager).pingRest(any());
    Mockito.verify(timeoutCalculator).getDefaultTimeout(Channel.UDP);
    Mockito.verifyNoMoreInteractions(pingManager, vehiclePingOutputConverter, vehiclePingRestMetricReporter, timeoutCalculator);
  }

  @Test
  void pingToVehicleWithInvalidVPITest() {
    PingToVehicleRequest request = new PingToVehicleRequest();
    request.setVehiclePlatformId("invalid-vpi");

    ResponseStatusException exception = assertThrows(ResponseStatusException.class,
        () -> vehiclePingRestHandlerV2.pingToVehicle(request));

    assertEquals(HttpStatus.BAD_REQUEST, exception.getStatusCode());
    Mockito.verify(timeoutCalculator).getDefaultTimeout(Channel.UDP);
    Mockito.verifyNoMoreInteractions(pingManager, vehiclePingOutputConverter, vehiclePingRestMetricReporter, timeoutCalculator);
  }
}
