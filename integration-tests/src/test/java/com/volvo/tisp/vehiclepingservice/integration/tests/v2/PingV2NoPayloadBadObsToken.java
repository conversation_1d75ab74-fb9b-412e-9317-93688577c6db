package com.volvo.tisp.vehiclepingservice.integration.tests.v2;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.jwk.ECKey;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.test.jms.TestJmsClient;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.IdpMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.Idpm2mMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.JWKSClientTestMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.MtDispatcherMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.StateRepositoryRestMockExtended;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.RestUtil;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.model.PingToVehicleRequest;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingDataResponse;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;
import com.volvo.tisp.vps.api.PingResponse;


@Component
public class PingV2NoPayloadBadObsToken {
  public static final String STATE_TIMEOUT_QUEUE = "LOCAL.LOCAL.LOCAL.VPS.STATE_TIMEOUT";
  private final JWKSClientTestMock jWKSClientTestMock;
  private final TestJmsClient jmsClient;
  private final MtDispatcherMock mtDispatcherMock;
  private final MtMessagePublisher mtMessagePublisher;
  private final PingEntityService pingEntityService;
  private final ServiceAccessTokenService serviceAccessTokenService;
  private final StateRepositoryRestMockExtended stateRepositoryRestMock;
  private final WebTestClient webTestClient;

  public PingV2NoPayloadBadObsToken(JWKSClientTestMock jWKSClientTestMock, TestJmsClient jmsClient, MtDispatcherMock mtDispatcherMock,
      MtMessagePublisher mtMessagePublisher, PingEntityService pingEntityService, ServiceAccessTokenService serviceAccessTokenService,
      StateRepositoryRestMockExtended stateRepositoryRestMock, WebTestClient webTestClient) {
    this.jWKSClientTestMock = jWKSClientTestMock;
    this.jmsClient = jmsClient;
    this.mtDispatcherMock = mtDispatcherMock;
    this.mtMessagePublisher = mtMessagePublisher;
    this.pingEntityService = pingEntityService;
    this.serviceAccessTokenService = serviceAccessTokenService;
    this.stateRepositoryRestMock = stateRepositoryRestMock;
    this.webTestClient = webTestClient;
  }

  private static String createObsToken(int badType, ECKey ecKey) throws JOSEException {
    return switch (badType) {
      case 1 -> // expired token
          JWKSClientTestMock.getSignedJwtToken(ecKey, "mh.w svc.256", Instant.now().minus(60, ChronoUnit.SECONDS));
      case 2 -> // Not signed
          JWKSClientTestMock.getPlainJwtToken(ecKey, "mh.w svc.256", Instant.now().plus(60, ChronoUnit.SECONDS));
      case 3 -> // Bad scope claim
          JWKSClientTestMock.getSignedJwtToken(ecKey, "mh.w", Instant.now().plus(60, ChronoUnit.SECONDS));
      default -> null;
    };
  }

  private static void verifyDataApi(String vpi, VehiclePingDataResponse vehiclePingDataResponse) {
    assertEquals(vpi, vehiclePingDataResponse.getVpi());
    assertNull(vehiclePingDataResponse.getPayloadSent());
    assertNull(vehiclePingDataResponse.getPayloadReceived());
    assertNotNull(vehiclePingDataResponse.getStartTime());
    assertNotNull(vehiclePingDataResponse.getStopTime());
    assertEquals(VehiclePingDataResponse.StatusEnum.FAILED, vehiclePingDataResponse.getStatus());
    assertNotNull(vehiclePingDataResponse.getErrorDescription());
    assertEquals("Timed out before getting response", vehiclePingDataResponse.getErrorDescription());
  }

  public void test(int badType) throws Exception {
    mtMessagePublisher.setMtDispatcherFeature(true);
    serviceAccessTokenService.setServiceAccessTokenValidationEnabled(true);

    Idpm2mMock.mockCallToFetchServiceAccessToken("mh.w svc.256", "clientId");
    ECKey ecKey = jWKSClientTestMock.createECKey();
    IdpMock.mockKeyQuery(ecKey);

    String vpi = TestUtil.stubTusSubscription(2);

    String obsToken = createObsToken(badType, ecKey);

    stateRepositoryRestMock.stubPush(Instant.now().plus(3, ChronoUnit.MINUTES), 200);

    PingToVehicleRequest pingToVehicleRequest = TestUtil.createPingToVehicleRequestUdp(vpi, false, null);
    // REST api call
    String correlationId = String.valueOf(
        RestUtil.verifyPostHttpResponse(webTestClient, "/v2/ping", obsToken, pingToVehicleRequest, PingResponse.class)
            .getCorrelationId());

    PingEntity pingEntity = pingEntityService.findByCorrelationId(correlationId).get();
    TestUtil.verifyPingEntityPending(vpi, pingEntity);

    // Respond with Pong
    mtDispatcherMock.checkAndReplyPongV2(true, obsToken);

    stateRepositoryRestMock.stubPop(correlationId, List.of(correlationId));
    timeOutState(correlationId);

    // Poll DB data
    Awaitility.await()
        .atMost(Duration.of(20, ChronoUnit.SECONDS))
        .until(() -> pingEntityService.findByCorrelationId(correlationId).join().getStatus() != Status.PENDING);
    PingEntity pingEntityFinalDb = pingEntityService.findByCorrelationId(correlationId).join();

    // assert DB data
    TestUtil.verifyPingEntity(Status.TIMEOUT, pingEntityFinalDb, vpi);

    // get REST data
    VehiclePingDataResponse vehiclePingDataResponse = RestUtil.verifyGetHttpResponse(webTestClient, "/v2/data/correlationId/" + correlationId, obsToken,
        VehiclePingDataResponse.class);

    // validate REST data
    verifyDataApi(vpi, vehiclePingDataResponse);

    IdpMock.verifyIdpWiremockTokenInteractions(1);
  }

  private void timeOutState(String refId) {
    TispContext.runInContext(() ->
        jmsClient
            .newMessage()
            .payload(refId)
            .messageType("statetimeout.txt")
            .messageVersion("1.0")
            .sendTo(STATE_TIMEOUT_QUEUE)
    );
  }
}