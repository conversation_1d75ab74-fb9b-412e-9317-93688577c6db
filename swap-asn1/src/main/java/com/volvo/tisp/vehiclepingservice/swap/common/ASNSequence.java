package com.volvo.tisp.vehiclepingservice.swap.common;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

import org.jdom2.Element;

/**
 * Title: ASNSequence. Description: Copyright: Copyright (c) 2003 Company:
 *
 * <AUTHOR> @version 1.0
 */
public class ASNSequence extends ASNValue {

  private static final long serialVersionUID = 1L;

  private boolean extendeble = false;
  private boolean extended = false;

  public ASNSequence() {
  }

  public ASNValue createObject() throws ASNException {
    return new ASNSequence();
  }

  public void decode(PERStream stream) throws ASNException {
    if (extendeble) {
      ASNBoolean extensionBit = new ASNBoolean();
      extensionBit.setAsnObjectName(getAsnObjectName() + ".extensionBit");
      extensionBit.decode(stream);
      extended = extensionBit.get();
    }
  }

  public void decode(Element oElement) throws ASNException {
    if (oElement.getName().equals(getAsnObjectName())) {
      List<?> elements = oElement.getChildren();

      for (int i = 0; i < elements.size(); i++) {
        Element elem = (Element) elements.get(i);

        Field field = getField(elem);
        Class<?> c = field.getType();

        Class[] args = new Class[] {Element.class};
        try {
          Method method = c.getMethod("decode", args);
          Object o = field.get(this);
          method.invoke(o, new Object[] {elem});
        } catch (NoSuchMethodException e) {
          throw new ASNException("No decode method found in class '" + c + "'");
        } catch (Exception e) {
          throw new ASNException("Got exception when calling method " + getClass() + ".decode", e);
        }
      }
    } else {
      throw new ASNException(
          "Element '"
              + oElement.getName()
              + "' can not be unserialized for ASN object of type '"
              + getAsnObjectName()
              + "'");
    }
  }

  public void encode(PERStream stream) throws ASNException {
    if (extendeble) {
      ASNBoolean extensionBit = new ASNBoolean();
      extensionBit.setAsnObjectName(getAsnObjectName() + ".extensionBit");
      extensionBit.set(extended);
      extensionBit.encode(stream);
    }
  }

  public void encode(Element oElement) throws ASNException {
  }

  public long encodedSize() {
    return isExtendeble() ? 1 : 0;
  }

  public boolean isExtendeble() {
    return extendeble;
  }

  public boolean isExtended() {
    return extended;
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
  }

  public void setExtendeble(boolean extendeble) {
    this.extendeble = extendeble;
  }

  public void setExtended(boolean extended) {
    this.extended = extended;
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
  }
}
