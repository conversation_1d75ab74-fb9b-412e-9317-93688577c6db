<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information -->
  <!-- +=============================================== -->
  <parent>
    <groupId>com.volvo.tisp.vehiclepingservice</groupId>
    <artifactId>vehicle-ping-service-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>vehicle-ping-service-server-impl</artifactId>
  <packaging>jar</packaging>
  <name>Vehicle Ping Service :: Server :: Implementation</name>
  <url/>

  <!-- +=============================================== -->
  <!-- | Section 2: Dependency (Management) -->
  <!-- +=============================================== -->
  <dependencies>
    <!-- Our API -->
    <dependency>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-client-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-database</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-server-swap-json</artifactId>
      <version>${project.version}</version>
    </dependency>

    <!-- New REST API -->
    <dependency>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-server-api-openapi-impl</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-server-swap-asn1</artifactId>
      <version>${project.version}</version>
    </dependency>

    <!-- MT DISPATCHER API -->
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>mt-message-client-json</artifactId>
    </dependency>

    <!-- MO ROUTER API -->
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>mo-router-client-json</artifactId>
    </dependency>

    <!-- STATE REPO -->
    <dependency>
      <groupId>com.wirelesscar.staterepository-client</groupId>
      <artifactId>staterepository-client-impl-rest</artifactId>
    </dependency>

    <!-- IDPM2M Client -->
    <dependency>
      <groupId>com.volvo.tisp.idpm2m</groupId>
      <artifactId>identityprovider-m2m-client-impl-http</artifactId>
    </dependency>

    <!-- ZSTD Compression -->
    <dependency>
      <groupId>com.github.luben</groupId>
      <artifactId>zstd-jni</artifactId>
    </dependency>

    <!-- Nimbus -->
    <dependency>
      <groupId>com.nimbusds</groupId>
      <artifactId>nimbus-jose-jwt</artifactId>
    </dependency>

    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-opus-client-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>

    <!-- TUS -->
    <dependency>
      <groupId>com.wirelesscar.telematicunitservice</groupId>
      <artifactId>telematicunitservice-client-impl-rest</artifactId>
    </dependency>

    <!-- METRICS -->
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-influx</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>

    <!-- MONGODB -->
    <dependency>
      <groupId>org.mongodb</groupId>
      <artifactId>mongodb-driver-sync</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
    </dependency>

    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>common-dto-lib</artifactId>
    </dependency>

    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-artemis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.wirelesscar.subscriptionrepository</groupId>
      <artifactId>subscriptionrepository-client-impl</artifactId>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>test-utils-lib</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>21</source>
          <target>21</target>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
