package com.volvo.tisp.vehiclepingservice.integration.tests.v2;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vehiclepingservice.integration.tests.mock.Idpm2mMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.JWKSClientTestMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.MtDispatcherMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.TceMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;

@Component
public class PingV1NoPayloadFromVehicle {
  private final JWKSClientTestMock jWKSClientTestMock;
  private final MtDispatcherMock mtDispatcherMock;
  private final MtMessagePublisher mtMessagePublisher;
  private final ServiceAccessTokenService serviceAccessTokenService;
  private final TceMock tceMock;

  public PingV1NoPayloadFromVehicle(JWKSClientTestMock jWKSClientTestMock, MtDispatcherMock mtDispatcherMock, MtMessagePublisher mtMessagePublisher,
      ServiceAccessTokenService serviceAccessTokenService, TceMock tceMock) {
    this.jWKSClientTestMock = jWKSClientTestMock;
    this.mtDispatcherMock = mtDispatcherMock;
    this.mtMessagePublisher = mtMessagePublisher;
    this.serviceAccessTokenService = serviceAccessTokenService;
    this.tceMock = tceMock;
  }

  public void test(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    mtMessagePublisher.setMtDispatcherFeature(mtDispatcher);
    serviceAccessTokenService.setServiceAccessTokenValidationEnabled(tokenValidation);

    if (mtDispatcher) {
      Idpm2mMock.mockCallToFetchServiceAccessToken("mh.w svc.256", "clientId");
    }

    String obsToken = null;
    if (tokenValidation) {
      obsToken = jWKSClientTestMock.setUpJwtToken();
    }

    String vpi = TestUtil.stubTusSubscription(2);

    long id;
    if (mtDispatcher) {
      id = mtDispatcherMock.sendPingV1(useMoRouter, obsToken);
    } else {
      id = tceMock.sendPingV1();
    }

    if (mtDispatcher) {
      mtDispatcherMock.checkPongV1(id, useMoRouter);
    } else {
      tceMock.verifyPongV1(id);
    }
  }
}
