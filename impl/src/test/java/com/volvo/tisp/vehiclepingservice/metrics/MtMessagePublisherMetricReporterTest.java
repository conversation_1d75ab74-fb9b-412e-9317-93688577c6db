package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class MtMessagePublisherMetricReporterTest {
  @Test
  void onMtPublisherFailure() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessagePublisherMetricReporter::new, (registry, reporter) -> {
      reporter.onMtPublisherFailure();
      MetricsReporterTestUtils.checkCounter(registry, 1, MtMessagePublisherMetricReporter.MT_MESSAGE_PUBLISHER, 
          Tags.of(MtMessagePublisherMetricReporter.TYPE, "failure"));
    });
  }

  @Test
  void onMtPublisherSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessagePublisherMetricReporter::new, (registry, reporter) -> {
      reporter.onMtPublisherSuccess();
      MetricsReporterTestUtils.checkCounter(registry, 1, MtMessagePublisherMetricReporter.MT_MESSAGE_PUBLISHER, 
          Tags.of(MtMessagePublisherMetricReporter.TYPE, "success"));
    });
  }
}
