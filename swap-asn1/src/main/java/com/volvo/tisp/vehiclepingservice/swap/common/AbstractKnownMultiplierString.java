package com.volvo.tisp.vehiclepingservice.swap.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.lang.SuppressForbidden;

@SuppressForbidden
public abstract class AbstractKnownMultiplierString extends AbstractStringType {
  private static final Logger logger = LoggerFactory.getLogger(AbstractKnownMultiplierString.class);

  public AbstractKnownMultiplierString(String name) {
    super(name);
  }

  public void decode(PERStream stream) throws ASNException {
    if (stream.isDebug()) {
      logger.debug(
          "S "
              + stream.position()
              + " "
              + this.getAsnObjectName()
              + "\t"
              + this.getClass().getSimpleName());
    }

    int size = this.decodeLength(stream);
    this.decodeValue(stream, size);
    if (stream.isDebug()) {
      logger.debug(
          "E "
              + stream.position()
              + " "
              + this.getAsnObjectName()
              + "\t"
              + this.getClass().getSimpleName());
    }
  }

  public void encode(PERStream stream) throws ASNException {
    if (this.value == null) {
      throw new ASNException("Cannot encode null value");
    } else {
      this.assertValidValue();
      this.encodeLength(stream);
      this.encodeValue(stream);
    }
  }

  public long encodedSize() {
    int bufBits;
    try {
      bufBits = this.getEncodedValueSizeInBits();
    } catch (ASNException var4) {
      throw new RuntimeException(var4.getMessage(), var4);
    }

    switch (this.constrainType) {
      case 1:
        return (long) bufBits;
      case 4:
        try {
          return PERStream.getLengthDeterminatorSizeInBits((long) this.value.length())
              + (long) bufBits;
        } catch (ASNException var3) {
          throw new RuntimeException(var3.getMessage(), var3);
        }
      default:
        return (long) (countBits(this.lowBoundary, this.highBoundary) + bufBits);
    }
  }

  protected abstract void assertValidValue() throws ASNException;

  protected int decodeLength(PERStream stream) throws ASNException {
    switch (this.constrainType) {
      case 1:
        return (int) this.highBoundary + 1;
      case 4:
        return (int) stream.decodeLengthDeterminant();
      default:
        return (int)
            (this.lowBoundary
                + stream.decodeInteger(countBits(this.lowBoundary, this.highBoundary)));
    }
  }

  protected abstract void decodeValue(PERStream var1, int var2) throws ASNException;

  protected void encodeLength(PERStream stream) throws ASNException {
    int len = this.value.length();
    if (this.constrainType == 4) {
      stream.encodeLengthDeterminant((long) len);
    } else if (this.constrainType != 1) {
      if ((long) len > this.highBoundary || (long) len < this.lowBoundary) {
        throw new ASNException(
            "String is out of range. Expected ("
                + this.lowBoundary
                + ".."
                + this.highBoundary
                + ") characters but was "
                + len);
      }

      stream.encodeInteger(
          (long) len - this.lowBoundary, countBits(this.lowBoundary, this.highBoundary));
    } else {
      int size = (int) this.highBoundary + 1;
      if (len != size) {
        throw new ASNException(
            "String is out of range. Expected " + size + " characters but was " + len);
      }
    }
  }

  protected abstract void encodeValue(PERStream var1) throws ASNException;

  protected abstract int getEncodedValueSizeInBits() throws ASNException;
}
