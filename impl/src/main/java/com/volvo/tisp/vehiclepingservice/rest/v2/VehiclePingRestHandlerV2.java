package com.volvo.tisp.vehiclepingservice.rest.v2;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.context.Vehicle;
import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.identifier.VehicleIdentifier;
import com.volvo.tisp.vehiclepingservice.converters.VehiclePingOutputConverter;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.BadRequestException;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.NotFoundException;
import com.volvo.tisp.vehiclepingservice.metrics.VehiclePingRestMetricReporter;
import com.volvo.tisp.vehiclepingservice.rest.api.DataApi;
import com.volvo.tisp.vehiclepingservice.rest.api.PingApi;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.model.PingToVehicleRequest;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingDataResponse;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingResponse;
import com.volvo.tisp.vehiclepingservice.util.ExceptionUtil;
import com.volvo.tisp.vehiclepingservice.util.TimeoutCalculator;

@RestController
@RequestMapping(VehiclePingRestHandlerV2.V2_PATH)
public class VehiclePingRestHandlerV2 implements PingApi, DataApi {
  public static final String V2_PATH = "/v2";
  private static final int MAX_LENGTH = 127000; // set in SWAP json
  private static final Logger logger = LoggerFactory.getLogger(VehiclePingRestHandlerV2.class);
  private final VehiclePingOutputConverter outputConverter;
  private final PingManager pingManager;
  private final TimeoutCalculator timeoutCalculator;
  private final VehiclePingRestMetricReporter vehiclePingRestMetricReporter;

  public VehiclePingRestHandlerV2(PingManager pingManager, VehiclePingOutputConverter outputConverter, TimeoutCalculator timeoutCalculator,
      VehiclePingRestMetricReporter vehiclePingRestMetricReporter) {
    this.pingManager = pingManager;
    this.outputConverter = outputConverter;
    this.timeoutCalculator = timeoutCalculator;
    this.vehiclePingRestMetricReporter = vehiclePingRestMetricReporter;
  }

  private static void validateVpi(String vpi) {
    try {
      VehicleIdentifier.fromString(vpi);
    } catch (Exception e) {
      logger.error("Bad VPI: {} in V2 Request", vpi, e);
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
    }
  }

  @Override
  public CompletableFuture<ResponseEntity<VehiclePingDataResponse>> getData(UUID correlationId) {
    logger.debug(Retention.SHORT, "Get Data for correlationId: {}", correlationId);
    vehiclePingRestMetricReporter.onDataRequest();
    return pingManager
        .getData(correlationId.toString())
        .thenApply(outputConverter::convert)
        .thenApply(response -> {
          vehiclePingRestMetricReporter.onDataSuccess();
          return ResponseEntity.ok(response);
        })
        .exceptionally(getDataThrowableResponseEntity(correlationId));
  }

  @Override
  public CompletableFuture<ResponseEntity<VehiclePingResponse>> pingToVehicle(PingToVehicleRequest pingToVehicleRequest) {
    final String vpi = pingToVehicleRequest.getVehiclePlatformId();
    final Channel finalChannel = pingToVehicleRequest.getChannel() == null ? Channel.UDP : pingToVehicleRequest.getChannel();
    final boolean copyPayload = Boolean.TRUE.equals(pingToVehicleRequest.getCopyPayload());

    RequestData request = getRequestData(pingToVehicleRequest, vpi, copyPayload, finalChannel);
    validateVpi(vpi);

    try (final TispContext.Scope ignored = TispContext.current().newScope()
        .vehicle(Vehicle.builder().vpi(VehicleIdentifier.fromString(vpi)))
        .activate()) {
      return validateValues(vpi, copyPayload, pingToVehicleRequest.getPayload())
          .thenCompose(
              v -> {
                vehiclePingRestMetricReporter.onPingRequest(finalChannel);
                return pingManager.pingRest(request);
              })
          .thenApply(
              correlationId -> {
                VehiclePingResponse resp = new VehiclePingResponse();
                resp.setCorrelationId(UUID.fromString(correlationId));
                vehiclePingRestMetricReporter.onPingSuccess();
                return ResponseEntity.ok(resp);
              })
          .exceptionally(pingToVehicleThrowableResponseEntity());
    }
  }

  private Function<Throwable, ResponseEntity<VehiclePingDataResponse>> getDataThrowableResponseEntity(UUID correlationId) {
    return throwable -> {
      if (ExceptionUtil.isCauseAssignableFrom(throwable, NotFoundException.class)) {
        vehiclePingRestMetricReporter.onDataNotFound();
        return ResponseEntity.notFound().build();
      }
      vehiclePingRestMetricReporter.onDataFailure();
      logger.error("Exception during getting DB data for correlationId: {}", correlationId, throwable);
      return ResponseEntity.internalServerError().build();
    };
  }

  private RequestData getRequestData(PingToVehicleRequest pingToVehicleRequest, String vpi, boolean copyPayload, Channel finalChannel) {
    Integer timeout = pingToVehicleRequest.getTimeout();
    RequestData request = new RequestData(vpi, copyPayload, pingToVehicleRequest.getPayload(), finalChannel,
        timeout == null ? timeoutCalculator.getDefaultTimeout(finalChannel) : timeout);
    logger.info("Ping Request to vehicle: {}, copyPayload: {}, hasPayload: {}, channel: {}, timeout: {}", request.vpi(), request.copyPayload(), request.body(),
        request.channel(), request.timeoutInSeconds());
    return request;
  }

  private Function<Throwable, ResponseEntity<VehiclePingResponse>> pingToVehicleThrowableResponseEntity() {
    return throwable -> {
      if (ExceptionUtil.isCauseAssignableFrom(throwable, BadRequestException.class)) {
        logger.error("Bad request: {}", throwable.getMessage());
        vehiclePingRestMetricReporter.onPingBadRequest();
        return ResponseEntity.badRequest().build();
      } else if (ExceptionUtil.isCauseAssignableFrom(throwable, NotFoundException.class)) {
        logger.error("Not found: {}", throwable.getMessage());
        vehiclePingRestMetricReporter.onPingNotFound();
        return ResponseEntity.notFound().build();
      }
      logger.error("Error: {}", throwable.getMessage());
      vehiclePingRestMetricReporter.onPingFailure();
      return ResponseEntity.internalServerError().build();
    };
  }

  private CompletableFuture<Void> validateValues(String vpi, Boolean copyPayload, String body) {
    boolean hasBody = StringUtils.hasLength(body);
    return CompletableFuture.completedFuture(null)
        .thenAccept(
            v -> {
              if (copyPayload && !hasBody) {
                logger.debug(Retention.SHORT, "Cannot copy payload if no payload is provided, vpi={}", vpi);
                throw new BadRequestException("Cannot copy payload if no payload is provided");
              }
              if (hasBody && body.length() > MAX_LENGTH) {
                throw new BadRequestException("Too long body");
              }
            });
  }
}
