<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

   <modelVersion>4.0.0</modelVersion>

   <!-- +=============================================== -->
   <!-- | Section 1: Project information -->
   <!-- +=============================================== -->
   <parent>
      <groupId>com.volvo.tisp.vehiclepingservice</groupId>
      <artifactId>vehicle-ping-service-server</artifactId>
      <version>0-SNAPSHOT</version>
   </parent>

   <groupId>com.wirelesscar.ngtp.cd.assemblies</groupId>
   <artifactId>vehicle-ping-service-server-deployable</artifactId>
   <packaging>pom</packaging>

   <name>Vehicle Ping Service :: Server :: Deployable Assembly</name>

   <properties>
      <deploy.engine.jar.version>243</deploy.engine.jar.version>

      <deploy.engine.maven.plugin.version>46</deploy.engine.maven.plugin.version>
      <deploy.engine.version>55</deploy.engine.version>
      <supervisor3x.version>51</supervisor3x.version>
      <jdk21.version>0</jdk21.version>

   </properties>


   <!-- +=============================================== -->
   <!-- | Section 2: Dependency (management) settings -->
   <!-- +=============================================== -->
   <dependencies>
      <!-- Internal dependencies -->
      <dependency>
         <groupId>com.volvo.tisp.vehiclepingservice</groupId>
         <artifactId>vehicle-ping-service-server-app</artifactId>
         <version>${project.version}</version>
         <type>jar</type>
      </dependency>

      <!-- Deployers -->
      <dependency>
         <groupId>com.wirelesscar.framework.deploy-engine</groupId>
         <artifactId>deploy-engine-jar-deployer</artifactId>
         <version>${deploy.engine.jar.version}</version>
         <classifier>bundle</classifier>
         <type>zip</type>
      </dependency>

      <!-- Deploy Engine -->
      <dependency>
         <groupId>com.wirelesscar.framework.deploy-engine</groupId>
         <artifactId>deploy-engine</artifactId>
         <version>${deploy.engine.version}</version>
         <type>pom</type>
         <exclusions>
            <exclusion>
               <groupId>org.supervisord</groupId>
               <artifactId>supervisor3x</artifactId>
            </exclusion>
         </exclusions>
      </dependency>

      <!-- External dependencies / Infrastructure -->
      <dependency>
          <groupId>net.java.jdk</groupId>
          <artifactId>jdk21-aarch64</artifactId>
          <version>${jdk21.version}</version>
          <type>npm</type>
          <scope>provided</scope>
      </dependency>
   </dependencies>

   <!-- +=============================================== -->
   <!-- | Section 3: Build settings -->
   <!-- +=============================================== -->
   <build>
      <plugins>
         <plugin>
            <groupId>com.wirelesscar.framework.deploy-engine</groupId>
            <artifactId>deploy-engine-maven-plugin</artifactId>
            <version>${deploy.engine.maven.plugin.version}</version>
            <executions>
               <execution>
                  <goals>
                     <goal>package</goal>
                  </goals>
               </execution>
            </executions>
         </plugin>
      </plugins>
   </build>
</project>
