package com.volvo.tisp.vehiclepingservice.database.repository;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.ReactiveMongoOperations;

import com.volvo.tisp.vehiclepingservice.database.entity.DatabaseSequence;

class MessageIdRepoTest extends RepositoryTestBase {

  @Autowired
  private ReactiveMongoOperations mongoOperations;

  @Autowired
  private MessageIdRepo messageIdRepo;

  @BeforeEach
  void setUp() {
    mongoOperations.dropCollection(DatabaseSequence.class).block();
  }

  @Test
  void getNextMessageIdSequenceShouldIncrementSequentially() throws ExecutionException, InterruptedException {
    // First call should return 1
    CompletableFuture<Long> firstResult = messageIdRepo.getNextMessageIdSequence();
    Assertions.assertEquals(1L, firstResult.get());

    // Second call should return 2
    CompletableFuture<Long> secondResult = messageIdRepo.getNextMessageIdSequence();
    Assertions.assertEquals(2L, secondResult.get());

    // Third call should return 3
    CompletableFuture<Long> thirdResult = messageIdRepo.getNextMessageIdSequence();
    Assertions.assertEquals(3L, thirdResult.get());
  }
}
