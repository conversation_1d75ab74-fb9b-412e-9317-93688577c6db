package com.volvo.tisp.vehiclepingservice.util;

import java.util.function.Predicate;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;

public class ExceptionUtil {

  @SuppressFBWarnings("CT_CONSTRUCTOR_THROW")
  private ExceptionUtil() {
    throw new IllegalStateException("Utility class");
  }

  private static boolean filterCause(Throwable t, Predicate<Throwable> predicate) {
    if (t == null) {
      return false;
    }

    return predicate.test(t) || filterCause(t.getCause(), predicate);
  }

  public static <T extends Throwable> boolean isCauseAssignableFrom(Throwable t, Class<T> type) {
    return filterCause(t, ex -> type.isAssignableFrom(ex.getClass()));
  }
}
