package com.volvo.tisp.vehiclepingservice.jms;

import static com.volvo.tisp.vehiclepingservice.domain.PingManager.SWAP_SERVICE_ID;

import java.util.Base64;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher.Message;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage.AcceptedCost;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage.Priority;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage.Ttl;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.InternalServerErrorException;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.NoSubscriberException;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;
import com.volvo.tisp.vehiclepingservice.metrics.MtDispatcherMetricReporter;

@Component
public class MtDispatcherMessagePublisher {
  private static final String SUBSCRIPTION_OPTION_SYSTEM = "SYSTEM";
  private static final Logger logger = LoggerFactory.getLogger(MtDispatcherMessagePublisher.class);

  private final MessagePublisher<MtDto> messagePublisher;
  private final ServiceAccessTokenService serviceAccessTokenService;
  private final ServiceFunctionCalculator serviceFunctionCalculator;
  private final MtDispatcherMetricReporter mtDispatcherMetricReporter;

  public MtDispatcherMessagePublisher(MessagePublisher.Builder builder, ServiceAccessTokenService serviceAccessTokenService,
      ServiceFunctionCalculator serviceFunctionCalculator, MtDispatcherMetricReporter mtDispatcherMetricReporter) {
    this.messagePublisher = builder.messageType(MessageTypes.MT_MESSAGE, MtDto.class).version(MessageTypes.VERSION_1_0, this::convert).build();
    this.serviceAccessTokenService = serviceAccessTokenService;
    this.serviceFunctionCalculator = serviceFunctionCalculator;
    this.mtDispatcherMetricReporter = mtDispatcherMetricReporter;
  }

  public MtMessage convert(MtDto dto) {
    String serviceFunction = serviceFunctionCalculator.calculateServiceFunction(dto.getChannel());
    String base64Payload = Base64.getEncoder().encodeToString(dto.getPayload());
    logger.debug(Retention.SHORT, "Base64 payload: {}", base64Payload);

    return createMtMessage(dto.getVpi(), dto.getServiceVersion(), dto.getCorrelationId(), base64Payload, dto.getServiceAccessToken(), serviceFunction,
        dto.getTimeout());
  }

  public MtMessage createMtMessage(Vpi vpi, int serviceVersion, String correlationId, String payload, String token, String serviceFunction, int timeout) {
    return new MtMessage()
        .withVehiclePlatformId(vpi.toString())
        .withCorrelationId(Optional.ofNullable(correlationId).orElseGet(() -> UUID.randomUUID().toString()))
        .withServiceId(SWAP_SERVICE_ID)
        .withServiceVersion(serviceVersion)
        .withServiceFunction(serviceFunction)
        .withServiceAccessToken(token)
        .withPayload(payload)
        .withPriority(Priority.HIGH)
        .withOverride(true)
        .withAcceptedCost(AcceptedCost.LOW)
        .withTtl(calculateTtl(timeout))
        .withTrackingId(TispContext.current().tid().toString());

    // KeyId and Payload Signature (if they are still exposed) is E2E Security and shouldn't be used
    // at least not if you haven't communicated with VC on why and how to do it
  }

  public CompletableFuture<Void> publish(MtDto dto) {
    return serviceAccessTokenService
        .getServiceAccessToken()
        .thenApply(dto::setServiceAccessToken)
        .thenCompose(this::createAndPublishMessage)
        .exceptionally(ex -> handlePublishError(ex, dto));
  }

  private Message<MtDto> buildMessage(MtDto updatedDto) {
    Message<MtDto> message = messagePublisher.newMessage();
    // Add correlation ID if present
    if (updatedDto.getCorrelationId() != null) {
      message = message.correlationId(updatedDto.getCorrelationId());
    }
    return message
        .option(SUBSCRIPTION_OPTION_SYSTEM, updatedDto.getSystem())
        .replyTo(MtStatusJmsHandler.MT_STATUS_IN_QUEUE);
  }

  private Ttl calculateTtl(int timeout) {
    if (timeout > (60 * 10)) {
      return Ttl.ONE_HOUR;
    } else if (timeout > (60 * 5)) {
      return Ttl.TEN_MINUTES;
    } else if (timeout > (60)) {
      return Ttl.FIVE_MINUTES;
    } else {
      return Ttl.ONE_MINUTE;
    }
  }

  private CompletableFuture<Void> createAndPublishMessage(MtDto updatedDto) {
    Message<MtDto> message = buildMessage(updatedDto);
    return publishMessageAndVerify(message, updatedDto);
  }

  private Void handlePublishError(Throwable ex, MtDto dto) {
    logger.error("Error publishing message vpi: {}", dto.getVpi(), ex);
    mtDispatcherMetricReporter.onPublishFailure();
    throw new InternalServerErrorException(ex);
  }

  private CompletableFuture<Void> publishMessageAndVerify(Message<MtDto> message, MtDto dto) {
    return message.publish(dto)
        .thenAccept(subscribers -> verifyAndLogPublish(subscribers, dto));
  }

  private void verifyAndLogPublish(int subscribers, MtDto dto) {
    if (subscribers == 0) {
      mtDispatcherMetricReporter.onPublishNoSubscriber();
      throw new NoSubscriberException(
          "No subscribers for MtMessage 1.0 for Ping (Mt Dispatcher)");
    }
    logger.info("Published message (MtDispatcher) to {} subscribers, vpi: {}, corrId: {}, serviceVersion: {}", subscribers, dto.getVpi(),
        dto.getCorrelationId(), dto.getServiceVersion());
    mtDispatcherMetricReporter.onPublishSuccess();
  }
}
