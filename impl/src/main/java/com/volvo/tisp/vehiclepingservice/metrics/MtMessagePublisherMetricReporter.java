package com.volvo.tisp.vehiclepingservice.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Metrics for handling incoming MO (mobile-originated) messages from vehicles.
 */
@Component
public class MtMessagePublisherMetricReporter {
  static final String MT_MESSAGE_PUBLISHER = "mt-message-publisher";
  static final String TYPE = "TYPE";
  private final Counter mtPublisherFailureCounter;
  private final Counter mtPublisherSuccessCounter;

  public MtMessagePublisherMetricReporter(MeterRegistry meterRegistry) {
    mtPublisherSuccessCounter = meterRegistry.counter(MT_MESSAGE_PUBLISHER, TYPE, "success");
    mtPublisherFailureCounter = meterRegistry.counter(MT_MESSAGE_PUBLISHER, TYPE, "failure");
  }

  public void onMtPublisherFailure() {
    mtPublisherFailureCounter.increment();
  }

  public void onMtPublisherSuccess() {
    mtPublisherSuccessCounter.increment();
  }
}