package com.volvo.tisp.vehiclepingservice.util;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.endpoint.OAuth2AccessTokenResponse;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.PlainJWT;
import com.volvo.tisp.vehiclepingservice.metrics.ServiceAccessTokenMetricReporter;

@ExtendWith(SpringExtension.class)
class ServiceAccessTokenClientTest {

  @InjectMocks
  private ServiceAccessTokenClient serviceAccessTokenClient;
  @Mock
  private ServiceAccessTokenMetricReporter serviceAccessTokenMetricReporter;
  @Mock
  private TokenFetcher tokenFetcher;

  private static String createToken(int expirationInMinutes) {
    return new PlainJWT(new JWTClaimsSet.Builder().claim("exp_mh", Instant.now().plus(expirationInMinutes, ChronoUnit.MINUTES).getEpochSecond()).build()).serialize();
  }

  @Test
  void fetchServiceAccessTokenTest() {
    String expectedToken = createToken(1);

    Mockito.when(tokenFetcher.postAccessTokenRequest(Mockito.anyString()))
        .thenReturn(
            CompletableFuture.completedFuture(OAuth2AccessTokenResponse.withToken(expectedToken).tokenType(OAuth2AccessToken.TokenType.BEARER).build()));

    String accessToken = serviceAccessTokenClient.fetchServiceAccessToken(List.of(256), List.of("mh.w")).join();
    Assertions.assertEquals(expectedToken, accessToken);
    Mockito.verify(tokenFetcher).postAccessTokenRequest(Mockito.anyString());

    expectedToken = createToken(11);
    Mockito.when(tokenFetcher.postAccessTokenRequest(Mockito.anyString()))
        .thenReturn(
            CompletableFuture.completedFuture(OAuth2AccessTokenResponse.withToken(expectedToken).tokenType(OAuth2AccessToken.TokenType.BEARER).build()));

    accessToken = serviceAccessTokenClient.fetchServiceAccessToken(List.of(256), List.of("mh.w")).join();
    Assertions.assertEquals(expectedToken, accessToken);
    Mockito.verify(tokenFetcher, Mockito.times(2)).postAccessTokenRequest(Mockito.anyString());

    accessToken = serviceAccessTokenClient.fetchServiceAccessToken(List.of(256), List.of("mh.w")).join();
    Assertions.assertEquals(expectedToken, accessToken);
    Mockito.verifyNoMoreInteractions(tokenFetcher);
  }
}
