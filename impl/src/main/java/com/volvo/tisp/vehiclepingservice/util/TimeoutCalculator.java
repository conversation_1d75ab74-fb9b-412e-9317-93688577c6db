package com.volvo.tisp.vehiclepingservice.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vehiclepingservice.rest.model.Channel;

@Component
public class TimeoutCalculator {
  private static final Logger logger = LoggerFactory.getLogger(TimeoutCalculator.class);

  private final int satDefaultTimeout;
  private final int smsDefaultTimeout;
  private final int udpDefaultTimeout;

  public TimeoutCalculator(
      @Value("${default.timeout.udp:300}") int udpTimeout,
      @Value("${default.timeout.sms:3600}") int smsTimeout,
      @Value("${default.timeout.sat:3600}") int satTimeout) {

    this.udpDefaultTimeout = udpTimeout;
    this.smsDefaultTimeout = smsTimeout;
    this.satDefaultTimeout = satTimeout;
    logger.info("Using udp default timeout {}", udpDefaultTimeout);
    logger.info("Using sms default timeout {}", smsDefaultTimeout);
    logger.info("Using satellite default timeout {}", satDefaultTimeout);
  }

  public int getDefaultTimeout(Channel channel) {
    return switch (channel) {
      case SMS -> smsDefaultTimeout;
      case SAT -> satDefaultTimeout;
      default -> udpDefaultTimeout;
    };
  }
}
