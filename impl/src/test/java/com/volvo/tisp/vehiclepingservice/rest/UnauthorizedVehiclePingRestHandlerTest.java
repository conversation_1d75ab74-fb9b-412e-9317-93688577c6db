package com.volvo.tisp.vehiclepingservice.rest;

import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.metrics.LegacyMetricsReporter;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;

@ExtendWith(SpringExtension.class)
public class UnauthorizedVehiclePingRestHandlerTest {

  @Mock
  private LegacyMetricsReporter legacyMetricsReporter;

  @Mock
  private PingManager pingManager;

  @InjectMocks
  private UnauthorizedVehiclePingRestHandler unauthorizedVehiclePingRestHandler;

  @Test
  void unauthorizedRequestPingEmptyVpiTest() {
    String emptyVpi = "   ";
    RequestData expectedRequestData = new RequestData("", false, null, Channel.UDP, 30);

    unauthorizedVehiclePingRestHandler.unauthorizedRequestPing(emptyVpi);

    Mockito.verify(legacyMetricsReporter).unauthorizedPingRequest();
    Mockito.verify(pingManager).pingRest(expectedRequestData);
    Mockito.verifyNoMoreInteractions(legacyMetricsReporter, pingManager);
  }

  @Test
  void unauthorizedRequestPingWithValidVPITest() {
    String vpi = "testVPI";
    RequestData expectedRequestData = new RequestData(vpi, false, null, Channel.UDP, 30);
    CompletableFuture<String> expectedFuture = CompletableFuture.completedFuture("Ping result");
    Mockito.when(pingManager.pingRest(expectedRequestData)).thenReturn(expectedFuture);

    CompletableFuture<String> result = unauthorizedVehiclePingRestHandler.unauthorizedRequestPing(vpi);

    Mockito.verify(legacyMetricsReporter).unauthorizedPingRequest();
    Mockito.verify(pingManager).pingRest(expectedRequestData);
    Assertions.assertEquals(expectedFuture, result);
    Mockito.verifyNoMoreInteractions(legacyMetricsReporter, pingManager);
  }

  @Test
  void unauthorizedVehiclePingRestHandlerConstructorTest() {
    Assertions.assertNotNull(new UnauthorizedVehiclePingRestHandler(pingManager, legacyMetricsReporter));
  }
}
