package com.volvo.tisp.vehiclepingservice.integration.tests.mock;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.io.IOUtils;
import org.eclipse.jetty.util.MultiMap;
import org.eclipse.jetty.util.UrlEncoded;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.OAuth2AccessToken.TokenType;
import org.springframework.security.oauth2.core.endpoint.DefaultOAuth2AccessTokenResponseMapConverter;
import org.springframework.security.oauth2.core.endpoint.OAuth2AccessTokenResponse;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.crypto.ECDSASigner;
import com.nimbusds.jose.jwk.ECKey;
import com.nimbusds.jose.util.JSONObjectUtils;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import com.volvo.tisp.omlibs.utils.datatransformation.DataFormatUtil;

// a copy of ServiceAccessTokenClientMock from idpm2mclient with a change of basePath from
// /idpm2m/oauth2/token to /oauth2/token
public final class Idpm2mMock {
  public static final long EXPIRES_IN = 25200L;
  public static final Instant IAT = Instant.now();
  public static final Date EXPIRY_30_DAYS = Date.from(IAT.plus(30L, ChronoUnit.DAYS));
  public static final Date EXPIRY_MESSAGE_HANDLER = Date.from(IAT.plus(9L, ChronoUnit.MINUTES));
  public static final String ISSUER_CLAIM = "https://api.local.local.vgcs.volvo.com/idpm2m/11114567-8901-2345-6789-012345678912";
  private static final String TENANT_JWKS_FILE = "private_jwks.json";
  private static DefaultOAuth2AccessTokenResponseMapConverter oauthToMap = new DefaultOAuth2AccessTokenResponseMapConverter();

  private Idpm2mMock() {
    throw new IllegalArgumentException();
  }

  public static void mockCallToFetchServiceAccessToken(
      String spaceSeparatedScopes, String clientId) {
    WireMock.stubFor(
        WireMock.post(WireMock.urlPathEqualTo("/oauth2/token"))
            .withHeader("Accept", WireMock.equalTo("application/json"))
            .withHeader("Content-Type", WireMock.containing("application/x-www-form-urlencoded"))
            .withRequestBody(WireMock.equalTo(createRequestBody(spaceSeparatedScopes)))
            .willReturn(
                WireMock.okForContentType(
                    "application/json",
                    createOAuth2AccessTokenResponse(clientId, spaceSeparatedScopes))));
  }

  private static String createJWT(String clientId, String scopes) {
    try {
      Map<String, Object> parsedJWK = JSONObjectUtils.parse(getTestPrivateJwks());
      parsedJWK = (Map) ((List) parsedJWK.get("keys")).get(0);
      JWSSigner signer = new ECDSASigner(ECKey.parse(parsedJWK));
      JWSHeader header = (new JWSHeader.Builder(JWSAlgorithm.ES384))
          .type(JOSEObjectType.JWT)
          .keyID(String.valueOf(parsedJWK.get("kid")))
          .build();

      JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
          .subject(clientId)
          .claim("ver", "1.0.0")
          .claim("exp_mh", EXPIRY_MESSAGE_HANDLER)
          .claim("scope", scopes)
          .issuer(ISSUER_CLAIM)
          .expirationTime(EXPIRY_30_DAYS)
          .issueTime(Date.from(IAT))
          .build();
      SignedJWT jwt = new SignedJWT(header, claimsSet);

      jwt.sign(signer);

      return jwt.serialize();
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  private static String createOAuth2AccessTokenResponse(String clientId, String scopes) {
    OAuth2AccessTokenResponse tokenResponse = OAuth2AccessTokenResponse.withToken(createJWT(clientId, scopes))
        .scopes(Set.of(scopes.split(" ")))
        .tokenType(TokenType.BEARER)
        .expiresIn(EXPIRES_IN)
        .build();

    return DataFormatUtil.toJson(oauthToMap.convert(tokenResponse));
  }

  private static String createRequestBody(String scopes) {
    MultiMap<String> map = new MultiMap();
    map.add("grant_type", AuthorizationGrantType.CLIENT_CREDENTIALS.getValue());
    map.add("scope", scopes);
    return UrlEncoded.encode(map, StandardCharsets.UTF_8, true);
  }

  private static String getTestPrivateJwks() throws IOException {
    return IOUtils.resourceToString(TENANT_JWKS_FILE, StandardCharsets.UTF_8, Idpm2mMock.class.getClassLoader());
  }
}
