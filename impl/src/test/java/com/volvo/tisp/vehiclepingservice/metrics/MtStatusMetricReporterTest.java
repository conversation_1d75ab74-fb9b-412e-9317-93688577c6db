package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class MtStatusMetricReporterTest {
  @Test
  void onCanceled() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (registry, reporter) -> {
      reporter.onCanceled();
      MetricsReporterTestUtils.checkCounter(registry, 1, MtStatusMetricReporter.MT_STATUS, Tags.of(MtStatusMetricReporter.TYPE, "canceled"));
    });
  }

  @Test
  void onDelivered() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (registry, reporter) -> {
      reporter.onDelivered();
      MetricsReporterTestUtils.checkCounter(registry, 1, MtStatusMetricReporter.MT_STATUS, Tags.of(MtStatusMetricReporter.TYPE, "delivered"));
    });
  }

  @Test
  void onFailed() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (registry, reporter) -> {
      reporter.onFailed();
      MetricsReporterTestUtils.checkCounter(registry, 1, MtStatusMetricReporter.MT_STATUS, Tags.of(MtStatusMetricReporter.TYPE, "failed"));
    });
  }

  @Test
  void onOverridden() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (registry, reporter) -> {
      reporter.onOverridden();
      MetricsReporterTestUtils.checkCounter(registry, 1, MtStatusMetricReporter.MT_STATUS, Tags.of(MtStatusMetricReporter.TYPE, "overridden"));
    });
  }

  @Test
  void onTimeout() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (registry, reporter) -> {
      reporter.onTimeout();
      MetricsReporterTestUtils.checkCounter(registry, 1, MtStatusMetricReporter.MT_STATUS, Tags.of(MtStatusMetricReporter.TYPE, "timeout"));
    });
  }
}