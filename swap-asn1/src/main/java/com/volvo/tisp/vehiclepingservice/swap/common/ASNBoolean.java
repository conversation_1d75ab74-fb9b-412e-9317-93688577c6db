package com.volvo.tisp.vehiclepingservice.swap.common;

import org.jdom2.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.lang.SuppressForbidden;

/**
 * Title: ASN Boolean class.
 *
 * <p>Description: Part of ASN
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR> @version 1.0
 */
@SuppressForbidden
public class ASNBoolean extends ASNConstrainedValue {
  private static final Logger logger = LoggerFactory.getLogger(ASNBoolean.class);

  /**
   *
   */
  private static final long serialVersionUID = 1L;

  private boolean m_value;

  public ASNBoolean() {
  }

  public ASNValue createObject() throws ASNException {
    return new ASNBoolean();
  }

  public void decode(PERStream stream) throws ASNException {
    if (stream.isDebug()) {
      logger.debug("S " + stream.position() + " " + getAsnObjectName() + "\tASNBoolean");
    }
    if (stream.decodeInteger(1) == 0x01) {
      m_value = true;
    } else {
      m_value = false;
    }
    if (stream.isDebug()) {
      logger.debug(
          "E " + stream.position() + " " + getAsnObjectName() + "\tASNBoolean (" + m_value + ")");
    }
  }

  public void decode(Element oElement) throws ASNException {
    set(Boolean.valueOf(oElement.getTextTrim()).booleanValue());
  }

  public void encode(PERStream stream) throws ASNException {
    stream.encodeBits((byte) (m_value ? 0x01 : 0x00), 1);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    oElem.addContent(m_value ? "true" : "false");
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    return 1;
  }

  public boolean get() {
    return m_value;
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_value = in.readBoolean();
  }

  public void set(boolean value) {
    m_value = value;
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    out.writeBoolean(m_value);
  }
}
