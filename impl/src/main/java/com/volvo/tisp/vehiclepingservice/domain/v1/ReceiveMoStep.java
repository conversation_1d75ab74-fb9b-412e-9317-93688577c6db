package com.volvo.tisp.vehiclepingservice.domain.v1;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.jms.MtDto;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.util.ChannelUtil;
import com.volvo.tisp.vehiclepingservice.util.TimeoutCalculator;
import com.wirelesscar.telematicunitservice.TelematicUnitService;

public abstract class ReceiveMoStep {
  private static final Logger logger = LoggerFactory.getLogger(ReceiveMoStep.class);

  private final MtMessagePublisher mtMessagePublisher;
  private final TelematicUnitService telematicUnitService;
  private final TimeoutCalculator timeoutCalculator;

  protected ReceiveMoStep(MtMessagePublisher mtMessagePublisher, TelematicUnitService telematicUnitService, TimeoutCalculator timeoutCalculator) {
    this.mtMessagePublisher = mtMessagePublisher;
    this.telematicUnitService = telematicUnitService;
    this.timeoutCalculator = timeoutCalculator;
  }

  public CompletableFuture<Void> action(MoDtoV1 moDtoV1) {
    MtDto sendDto = createMtDto(moDtoV1);

    return telematicUnitService
        .getSystem(moDtoV1.getVpi().toString())
        .thenApply(
            system -> {
              sendDto.setSystem(system);
              logger.debug(Retention.SHORT, "Got System: {} for vpi: {}", system, sendDto.getVpi());
              return sendDto;
            })
        .thenCompose(mtMessagePublisher::publishMtMessage);
  }

  protected abstract byte[] getPayload(MoDtoV1 moDtoV1);

  private MtDto createMtDto(MoDtoV1 moDtoV1) {
    MtDto sendDto = new MtDto();
    sendDto.setVpi(moDtoV1.getVpi());
    sendDto.setCorrelationId(moDtoV1.getCorrelationId());
    sendDto.setPayload(getPayload(moDtoV1));
    sendDto.setChannel(ChannelUtil.calculateResponseChannel(moDtoV1.getReceivingChannel()));
    sendDto.setTimeout(timeoutCalculator.getDefaultTimeout(sendDto.getChannel()));
    sendDto.setServiceVersion(1);
    return sendDto;
  }
}
