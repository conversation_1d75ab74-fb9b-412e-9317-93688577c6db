package com.volvo.tisp.vehiclepingservice.rest;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;

@RestController
public class RuntimeInfoRestController {

  @RequestMapping(
      value = "/runtimeinfo",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public Config get() {
    return ConfigFactory.getConfig();
  }
}
