package com.volvo.tisp.vehiclepingservice.metrics;

import java.util.Locale;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vehiclepingservice.rest.model.Channel;

import io.micrometer.core.instrument.Tags;

class VehiclePingRestMetricReporterTest {
  @Test
  void onDataFailure() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onDataFailure();
      MetricsReporterTestUtils.checkCounter(registry, 1, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.DATA_REQUEST, VehiclePingRestMetricReporter.TYPE, "failure"));
    });
  }

  @Test
  void onDataNotFound() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onDataNotFound();
      MetricsReporterTestUtils.checkCounter(registry, 1, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.DATA_REQUEST, VehiclePingRestMetricReporter.TYPE, "not-found"));
    });
  }

  @Test
  void onDataRequest() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onDataRequest();
      MetricsReporterTestUtils.checkCounter(registry, 1, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.DATA_REQUEST, VehiclePingRestMetricReporter.TYPE, "request"));
    });
  }

  @Test
  void onDataSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onDataSuccess();
      MetricsReporterTestUtils.checkCounter(registry, 1, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.DATA_REQUEST, VehiclePingRestMetricReporter.TYPE, "success"));
    });
  }

  @Test
  void onPingBadRequest() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onPingBadRequest();
      MetricsReporterTestUtils.checkCounter(registry, 1, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.PING_TO_VEHICLE, VehiclePingRestMetricReporter.TYPE, "bad-request"));
    });
  }

  @Test
  void onPingFailure() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onPingFailure();
      MetricsReporterTestUtils.checkCounter(registry, 1, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.PING_TO_VEHICLE, VehiclePingRestMetricReporter.TYPE, "failure"));
    });
  }

  @Test
  void onPingNotFound() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onPingNotFound();
      MetricsReporterTestUtils.checkCounter(registry, 1, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.PING_TO_VEHICLE, VehiclePingRestMetricReporter.TYPE, "not-found"));
    });
  }

  @Test
  void onPingRequest() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      Channel channel = Channel.UDP;
      reporter.onPingRequest(channel);
      MetricsReporterTestUtils.checkCounter(registry, 1, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of("request.channel", channel.getValue().toLowerCase(Locale.ROOT)));
    });
  }

  @Test
  void onPingSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(VehiclePingRestMetricReporter::new, (registry, reporter) -> {
      reporter.onPingSuccess();
      MetricsReporterTestUtils.checkCounter(registry, 1, VehiclePingRestMetricReporter.VEHICLE_PING,
          Tags.of(VehiclePingRestMetricReporter.FLOW, VehiclePingRestMetricReporter.PING_TO_VEHICLE, VehiclePingRestMetricReporter.TYPE, "success"));
    });
  }
}