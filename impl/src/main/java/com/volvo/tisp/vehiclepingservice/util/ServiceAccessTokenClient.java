package com.volvo.tisp.vehiclepingservice.util;

import java.text.ParseException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.util.Assert;

import com.nimbusds.jwt.JWT;
import com.nimbusds.jwt.JWTParser;
import com.volvo.tisp.idpm2m.client.config.Constants;
import com.volvo.tisp.vehiclepingservice.metrics.ServiceAccessTokenMetricReporter;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;

import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

// a copy from idpm2mclient with a change of return type for fetchServiceAccessToken
public class ServiceAccessTokenClient {
  private static final Config config = ConfigFactory.getConfig();

  private final Map<String, Tuple2<String, Instant>> bosServiceAccessTokenCache;
  private final ServiceAccessTokenMetricReporter serviceAccessTokenMetricReporter;
  private final TokenFetcher tokenFetcher;

  public ServiceAccessTokenClient(TokenFetcher tokenFetcher, ServiceAccessTokenMetricReporter serviceAccessTokenMetricReporter) {
    this.tokenFetcher = tokenFetcher;
    this.serviceAccessTokenMetricReporter = serviceAccessTokenMetricReporter;
    this.bosServiceAccessTokenCache = new ConcurrentHashMap<>(10);
  }

  public CompletableFuture<String> fetchServiceAccessToken(List<Integer> serviceIdScopeList, List<String> rawScopeList) {
    return fetchServiceAccessToken(serviceIdScopeList, rawScopeList, getCacheTimeMinutesConfig());
  }

  private CompletableFuture<String> fetchAndCacheToken(String scopeParameter) {
    serviceAccessTokenMetricReporter.onRetrievingNewToken();
    return tokenFetcher
        .postAccessTokenRequest(scopeParameter)
        .thenApply(
            tokenResponse -> {
              try {
                String compactJWTAccessTokenString = tokenResponse.getAccessToken().getTokenValue();
                JWT parsedJWT = JWTParser.parse(compactJWTAccessTokenString);
                Instant tokenExpiryTime =
                    parsedJWT.getJWTClaimsSet().getDateClaim("exp_mh").toInstant();
                bosServiceAccessTokenCache.put(
                    scopeParameter, Tuples.of(compactJWTAccessTokenString, tokenExpiryTime));
                serviceAccessTokenMetricReporter.onTokenSuccess();
                return tokenResponse.getAccessToken().getTokenValue();
              } catch (ParseException e) {
                serviceAccessTokenMetricReporter.onTokenFailure();
                throw new RuntimeException(e);
              }
            })
        .exceptionally(throwable -> {
          serviceAccessTokenMetricReporter.onTokenFailure();
          throw new RuntimeException(throwable);
        });
  }

  private CompletableFuture<String> fetchServiceAccessToken(List<Integer> serviceIdScopeList, List<String> rawScopeList, int cachedTokenWantedLifespanMinutes) {
    String scopeParameter = formatScopes(serviceIdScopeList, rawScopeList);
    int cachedTokenLifespanMin =
        cachedTokenWantedLifespanMinutes >= 60 || cachedTokenWantedLifespanMinutes < 0
            ? getCacheTimeMinutesConfig()
            : cachedTokenWantedLifespanMinutes;
    Instant tokenMinimumExpiryTime = Instant.now().plus(cachedTokenLifespanMin, ChronoUnit.MINUTES);
    Tuple2<String, Instant> cachedToken = bosServiceAccessTokenCache.get(scopeParameter);
    if (cachedToken == null) {
      return fetchAndCacheToken(scopeParameter);
    }
    if (cachedToken.getT2().isAfter(tokenMinimumExpiryTime)
        || cachedToken.getT2().equals(tokenMinimumExpiryTime)) {
      serviceAccessTokenMetricReporter.onReuseToken();
      return CompletableFuture.completedFuture(cachedToken.getT1());
    } else {
      return fetchAndCacheToken(scopeParameter);
    }
  }

  private String formatScopes(Collection<Integer> serviceIdScopeList, Collection<String> rawScopeList) {
    Assert.notEmpty(serviceIdScopeList, "Service id scope list can not be empty");
    Set<String> scopeParameterSet =
        serviceIdScopeList.stream()
            .map(serviceId -> "svc." + serviceId)
            .collect(Collectors.toSet());
    scopeParameterSet.addAll(Constants.MESSAGE_HANDLER_SCOPES);
    scopeParameterSet.addAll(rawScopeList);
    String scopeParameter =
        scopeParameterSet.stream()
            .map(s -> s.toLowerCase(Locale.ROOT).trim())
            .sorted()
            .collect(Collectors.joining(" "));
    Assert.hasLength(scopeParameter, "Scope parameter can not be empty");
    return scopeParameter;
  }

  private int getCacheTimeMinutesConfig() {
    return config
        .getInt("oauth2.idpm2m.token-cache-time-minutes")
        .orElse(Constants.ACCESS_TOKEN_LIFESPAN_DEFAULT_MINUTES);
  }
}
