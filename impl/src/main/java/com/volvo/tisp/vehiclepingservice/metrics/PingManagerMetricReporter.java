package com.volvo.tisp.vehiclepingservice.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Metrics for processing MO (mobile-originated) messages from vehicles using V1 protocol.
 */
@Component
public class PingManagerMetricReporter {
  static final String PING = "PING";
  static final String PING_MANAGER = "ping-manager";
  static final String PONG = "PONG";
  static final String VEHICLE = "VEHICLE";
  static final String VERSION = "VERSION";

  private final Counter beefyFromVehicle;
  private final Counter beefyResponseFromVehicle;
  private final Counter pingFromVehicleV1;
  private final Counter pingFromVehicleV2;
  private final Counter pongFromVehicleV1;
  private final Counter pongFromVehicleV2;

  public PingManagerMetricReporter(MeterRegistry meterRegistry) {
    beefyFromVehicle = meterRegistry.counter(PING_MANAGER, "beefy", VEHICLE);
    beefyResponseFromVehicle = meterRegistry.counter(PING_MANAGER, "beefyresponse", VEHICLE);
    pingFromVehicleV1 = meterRegistry.counter(PING_MANAGER, PING, VEHICLE, VERSION, "v1");
    pingFromVehicleV2 = meterRegistry.counter(PING_MANAGER, PING, VEHICLE, VERSION, "v2");
    pongFromVehicleV1 = meterRegistry.counter(PING_MANAGER, PONG, VEHICLE, VERSION, "v1");
    pongFromVehicleV2 = meterRegistry.counter(PING_MANAGER, PONG, VEHICLE, VERSION, "v2");
  }

  public void onBeefyFromVehicle() {
    beefyFromVehicle.increment();
  }

  public void onBeefyResponseFromVehicle() {
    beefyResponseFromVehicle.increment();
  }

  public void onPingFromVehicleV1() {
    pingFromVehicleV1.increment();
  }

  public void onPingFromVehicleV2() {
    pingFromVehicleV2.increment();
  }

  public void onPongFromVehicleV1() {
    pongFromVehicleV1.increment();
  }

  public void onPongFromVehicleV2() {
    pongFromVehicleV2.increment();
  }
}
