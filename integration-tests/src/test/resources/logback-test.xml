<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%date{HH:mm:ss.SSS} [%thread] [%.-1level] %logger{48} - %message%n</pattern>
    </encoder>
  </appender>

  <logger name="com.volvo.tisp.vehiclepingservice" level="DEBUG"/>
  <logger name="org.springframework.web.reactive.function.client" level="DEBUG"/>
  <logger name="com.volvo.tisp.framework" level="DEBUG"/>

  <root level="INFO">
    <appender-ref ref="STDOUT"/>
  </root>
</configuration>
