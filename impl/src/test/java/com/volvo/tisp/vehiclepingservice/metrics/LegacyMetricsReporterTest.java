package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class LegacyMetricsReporterTest {

  @Test
  void legacyGetDataRequest() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGetDataRequest();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "getdata"));
    });
  }

  @Test
  void legacyGuiBeefyPermission() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGuiBeefyPermission();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "guibeffypermission"));
    });
  }

  @Test
  void legacyGuiCustomPermission() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyGuiCustomPermission();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "guicustompermission"));
    });
  }

  @Test
  void legacyPingRequest() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.legacyPingRequest();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "authorized"));
    });
  }

  @Test
  void unauthorizedPingRequest() {
    MetricsReporterTestUtils.initReporterAndTest(LegacyMetricsReporter::new, (meterRegistry, metricsReporter) -> {
      metricsReporter.unauthorizedPingRequest();
      MetricsReporterTestUtils.checkCounter(meterRegistry, 1, LegacyMetricsReporter.TOP_LEVEL_METRIC_NAME, Tags.of("legacy", "unauthorized"));
    });
  }
}
