package com.volvo.tisp.vehiclepingservice.swap.common;

import java.io.Externalizable;
import java.io.IOException;
import java.io.ObjectOutput;
import java.util.Arrays;

/**
 * Title: PER Stream class.
 *
 * <p>Description: Part of ASN. The stream that holds the serialized data
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR> @version 1.0
 */
public class PERStream implements Externalizable {
  /**
   *
   */
  private static final long serialVersionUID = 1L;
  boolean bDebug = false;
  // Stores how many bits in the active byte that are not yeat used or read.
  int bitPointer = 8;
  byte[] buffer = null;
  // Index into the buffer, marking the byte next to be handled.
  int bytePointer = 0;

  public PERStream() {
    buffer = new byte[1024];
  }

  public PERStream(int Size) {
    buffer = new byte[Size];
  }

  public PERStream(byte[] buf) {
    this.buffer = Arrays.copyOf(buf, buf.length);
  }

  static long getLengthDeterminatorSizeInBits(long count) throws ASNException {
    if (count < 128) {
      return 8;
    } else if (count < 16384) {
      return 16;
    } else {
      int multiplier = getMultiplier(count);
      return 8 + getLengthDeterminatorSizeInBits(count - (multiplier * 16384));
    }
  }

  private static int getMultiplier(long count) {
    return Math.min(4, (int) (count / 16384));
  }

  public void alignOnByte() {
    if (bitPointer != 8) {
      bitPointer = 8;
      bytePointer++;
    }
  }

  /**
   * Adds a PERStream onto this stream. This operation is always byte aligned. If any of the streams
   * are used, I.e. the read/write pointer is not zero, only the used part will be included into the
   * combined stream.
   *
   * @param oAddonStream The stream to add to this
   */
  public void append(PERStream oAddonStream) {
    // Check if this is unwritten and unread.
    if (bytePointer == 0 && bitPointer == 8) {
      // Check if also oAddonStream is unused.
      if (oAddonStream.bytePointer == 0 && oAddonStream.bitPointer == 8) {
        rawAppend(oAddonStream.buffer);
      } else {
        rawAppend(buffer.length, oAddonStream.buffer, 0, oAddonStream.bytePointer);
      }
    } else {
      // Check if also oAddonStream is unused.
      if (oAddonStream.bytePointer == 0 && oAddonStream.bitPointer == 8) {
        rawAppend(bytePointer, oAddonStream.buffer, 0, oAddonStream.buffer.length);
      } else {
        rawAppend(bytePointer, oAddonStream.buffer, 0, oAddonStream.bytePointer);
      }
    }
    // append( bytePointer, byAddon, iSourcePos, iSrcLength);
  }

  public void attachBuffer(byte[] buf) {
    this.buffer = Arrays.copyOf(buf, buf.length);
  }

  public int bits() {
    return bytePointer * 8 + (8 - bitPointer);
  }

  public int bitsLeft() {
    return buffer.length * 8 - bits();
  }

  public void checkAndAlign() {
    if (bitPointer == 0) {
      bitPointer = 8;
      bytePointer++;
    }
  }

  /**
   * Align the pointers
   */
  public void endDecoding() {
    alignOnByte();
  }

  /**
   * Align the pointers
   */
  public void endEncoding() {
    alignOnByte();
  }

  public byte[] getBuffer() {
    int iLength = bytePointer;
    if (bitPointer != 8) {
      iLength++;
    }
    byte[] retVal = new byte[iLength];
    for (int i = 0; i < iLength; i++) {
      retVal[i] = buffer[i];
    }
    return retVal;
  }

  public byte[] getUnusedBufferPart() throws ASNException {
    int iLength = bytePointer;
    int iSize = buffer.length - iLength;
    if (bitPointer != 8) {
      throw new ASNException("Stream must be aligned on byte for this!");
    }
    byte[] retVal = new byte[iSize];
    for (int i = 0; i < iSize; i++) {
      retVal[i] = buffer[iLength + i];
    }
    return retVal;
  }

  public boolean isDebug() {
    return bDebug;
  }

  public String position() {
    return bytePointer + "." + (8 - bitPointer);
  }

  /**
   * Adds a buffer to the existing PERStream buffer regardless of where the read/write pointer is
   * located.
   *
   * @param byAddon The buffer to append.
   */
  public void rawAppend(byte[] byAddon) {
    byte[] byBoth = new byte[buffer.length + byAddon.length];
    System.arraycopy(buffer, 0, byBoth, 0, buffer.length);
    System.arraycopy(byAddon, 0, byBoth, buffer.length, byAddon.length);
    buffer = byBoth;
    bytePointer = buffer.length;
    bitPointer = 8;
  }

  /**
   * Appends parts of a buffer to this PERStream.
   *
   * @param iDestPos   The position in the current stream where byAddon is written.
   * @param byAddon    The buffer to append.
   * @param iSourcePos Starting position in byAddon.
   * @param iSrcLength The amount of bytes to be appended.
   */
  public void rawAppend(int iDestPos, byte[] byAddon, int iSourcePos, int iSrcLength) {
    // Can whe fit in the new data into the buffer whe have?
    if (buffer.length >= (iDestPos + iSrcLength)) {
      System.arraycopy(byAddon, iSourcePos, buffer, iDestPos, iSrcLength);
      bytePointer = iDestPos + iSrcLength;
      bitPointer = 8;
    } else {
      // Make a new buffer big enough for both parts of the two buffers.
      byte[] byBoth = new byte[iDestPos + iSrcLength];
      // Copy from the original buffer bytes 0-iDestPos to the newly created buffer.
      System.arraycopy(buffer, 0, byBoth, 0, iDestPos);
      // Copy the new data into the new buffer
      System.arraycopy(byAddon, iSourcePos, byBoth, iDestPos, iSrcLength);
      buffer = byBoth;
      bytePointer = iDestPos + iSrcLength;
      bitPointer = 8;
    }
  }

  public void readExternal(java.io.ObjectInput in) throws IOException, ClassNotFoundException {
    bytePointer = in.readInt();
    bDebug = in.readBoolean();
    bitPointer = in.readInt();
    int size = in.readInt();
    if (size > 0) {
      buffer = new byte[size];
      for (int i = 0; i < size; i++)
        buffer[i] = in.readByte();
    }
  }

  public void setBits(int b) {
    bytePointer = b / 8;
    bitPointer = 8 - b % 8;
  }

  public void setDebug(boolean debug) {
    this.bDebug = debug;
  }

  public void setPosition(int iNewPosition) {
    bitPointer = 8;
    bytePointer = iNewPosition;
  }

  /**
   * Reset the pointers to enable the user to decode a message once more from start
   */
  public void startDecoding() {
    bitPointer = 8;
    bytePointer = 0;
  }

  /**
   * Clear the buffer and reset the pointers to enable reuse of an encode buffer
   */
  public void startEncoding() {
    bitPointer = 8;
    bytePointer = 0;
    for (int i = 0; i < buffer.length; i++)
      buffer[i] = 0;
  }

  public void writeExternal(ObjectOutput out) throws IOException {
    out.writeInt(bytePointer);
    out.writeBoolean(bDebug);
    out.writeInt(bitPointer);
    if (buffer != null) {
      out.writeInt(buffer.length);
      for (int i = 0; i < buffer.length; i++)
        out.writeByte(buffer[i]);
    } else {
      out.writeInt(0);
    }
  }

  byte decodeBits(int bitCount) throws ASNException {
    if (bitsLeft() < bitCount) {
      throw new ASNException("Not enough bits in buffer (" + bitCount + ">" + bitsLeft() + ")!");
    }
    byte data;
    if (bitCount <= bitPointer) {
      byte mask = (byte) 0xff;
      // The Bit String fits within the current Byte.
      data = buffer[bytePointer];
      data >>= (bitPointer - bitCount);
      mask <<= bitCount;
      data &= ~mask;
      bitPointer -= bitCount;
      checkAndAlign();
    } else {
      int leftovers = bitCount - bitPointer;
      data = decodeBits(bitPointer);
      data <<= leftovers;
      byte leftoverPart = decodeBits(leftovers);
      data |= leftoverPart;
    }
    return data;
  }

  byte decodeByte() throws ASNException {
    if (bitsLeft() < 8) {
      throw new ASNException("Not enough bits in buffer (" + "8>" + bitsLeft() + ")!");
    }
    if (bitPointer == 8) {
      return buffer[bytePointer++];
    } else {
      return decodeBits(8);
    }
  }

  long decodeInteger(int bitCount) throws ASNException {
    long value = 0;
    while (bitCount > 0) {
      if (bitCount < 8) {
        value = (value << bitCount) + decodeBits(bitCount);
        bitCount = 0;
      } else {
        byte part = decodeByte();
        value = (value << 8) + ((part & 0x80) == 0 ? (int) part : (part & (byte) 0x7f) + 0x80);
        bitCount -= 8;
      }
    }
    return value;
  }

  /*
   * *******************************************************************
   *
   * Length determinant encode/decode
   *
   * Implementation based on ASN1_Complete.pdf (page 292ff). See also dubuisson-asn1-book.PDF (page 438-439)
   *
   * "First form" of encoding. Encoded as single octet. Values 0-127
   *
   * |8|7|6|5|4|3|2|1| ________________ | | | |0| Count | |_|_____________|
   *
   * "Second form" of encoding. Encoded as two octets. Values 128-16383
   *
   * |8|7|6|5|4|3|2|1| ________________ | | | Count | |1|0|(top 6bits)| |_|_|___________| ________________ | Count | |(bottom 8 bits)| |_______________|
   *
   * "Third form" of encoding ("infinite" form). Values 16384 and larger.
   *
   * |8|7|6|5|4|3|2|1| ______________________ | | | # 16K Units | |1|1| (6bits, max 4) | |_|_|_________________| ________________ | Up to 65K | | Units |
   * |_______________|
   *
   * ______________________ | | | |0| Count (7 bits) | New Length determinant |_|___________________|
   *
   */
  // If the returned value is >= 16384 then the caller must first decode the returned number
  // of Units, then call this method again to get the following number of Units.
  long decodeLengthDeterminant() throws ASNException {
    int firstBit = decodeBits(1);
    if (firstBit == 0) {
      // First form
      return decodeInteger(7);
    } else {
      int secondBit = decodeBits(1);
      if (secondBit == 0) {
        // Second form
        return decodeInteger(14);
      } else {
        // Third form
        long multiplier = decodeInteger(6);
        return multiplier * 16384;
      }
    }
  }

  long decodeLittleEndianInteger(int bitCount) throws ASNException {
    if (bitCount % 8 != 0) {
      throw new ASNException("Little Endian object size must be a multiple of 8");
    }

    long value = 0;
    long shiftedPart;
    byte part;
    for (int i = 0; i < bitCount / 8; i++) {
      part = decodeByte();
      shiftedPart = ((part & 0x80) == 0 ? (int) part : (part & (byte) 0x7f) + 0x80);
      value += shiftedPart << (i * 8);
    }
    return value;
  }

  void encodeBits(byte data, int bitCount) throws ASNException {
    if (bitsLeft() < bitCount) {
      throw new ASNException("Buffer full!");
    }
    byte mask = (byte) 0xff;
    mask <<= bitCount;
    data &= ~mask;
    if (bitCount <= bitPointer) {
      // The Bit String fits within the last Byte.
      data <<= (bitPointer - bitCount);
      buffer[bytePointer] |= data;
      bitPointer -= bitCount;
      checkAndAlign();
    } else {
      int leftovers = bitCount - bitPointer;
      byte firstPart = data;
      firstPart >>= (bitCount - bitPointer);
      encodeBits(firstPart, bitPointer);
      mask = (byte) 0xff;
      mask <<= leftovers;
      encodeBits((byte) (data & ~mask), leftovers);
    }
  }

  void encodeByte(byte data) throws ASNException {
    if (bitsLeft() < 8) {
      throw new ASNException("Buffer full!");
    }
    if (bitPointer == 8) {
      buffer[bytePointer++] = data;
    } else {
      encodeBits(data, 8);
    }
  }

  /**
   * @param value
   * @param bitCount
   * @throws ASNException
   */
  void encodeInteger(long value, int bitCount) throws ASNException {
    if (bitCount <= 0) {
      return;
    }
    long mask = ~(~0L << bitCount);
    byte valuePart;
    if (value != (value & mask)) {
      throw new ASNException("Integer value too big (" + value + ")!");
    }
    while (bitCount > 8) {
      mask = 255L << (bitCount - 8);
      valuePart = (byte) ((value & mask) >> (bitCount - 8));
      encodeByte(valuePart);
      bitCount -= 8;
    }
    mask = (~(~0L << bitCount)) & 0xff;
    valuePart = (byte) (value & mask);
    encodeBits(valuePart, bitCount);
  }

  /*
   * * If return value != count, then the caller shall first encode the returned number of units and then call this method again with the remaining count.
   */
  long encodeLengthDeterminant(long count) throws ASNException {
    if (count < 0) {
      throw new ASNException("The count in length determinant cannot be negative (" + count + ")");
    }
    if (count < 128) {
      // First form
      encodeBits((byte) 0, 1);
      encodeInteger(count, 7);
      return count;
    } else if (count < 16384) {
      // Second form
      encodeBits((byte) 1, 1);
      encodeBits((byte) 0, 1);
      encodeInteger(count, 14);
      return count;
    } else {
      // Third form
      encodeBits((byte) 1, 1);
      encodeBits((byte) 1, 1);
      int multiplier = getMultiplier(count);
      encodeBits((byte) multiplier, 6);
      return multiplier * 16384L;
    }
  }

  /**
   * @param value
   * @param bitCount
   * @throws ASNException
   */
  void encodeLittleEndianInteger(long value, int bitCount) throws ASNException {
    if (bitCount % 8 != 0) {
      throw new ASNException("Little Endian object size must be a multiple of 8");
    }

    long mask = ~(~0L << bitCount);
    if (value != (value & mask)) {
      throw new ASNException("Integer value too big!");
    }

    for (int i = 0; i < bitCount / 8; i++) {
      encodeByte((byte) ((value >> (i * 8)) & 255L));
    }
  }

  long peekInteger(int bitCount) throws ASNException {
    int pos = bits();
    long value = decodeInteger(bitCount);
    setBits(pos);
    return value;
  }

  void postEncodeSwapByteOrder(int iSize) throws ASNException {
    if (bitPointer != 8) {
      throw new ASNException("The object must start on a byte");
    }
    // Check if there are enough room to swap that many bytes
    if (iSize > bytePointer) {
      throw new ASNException("Out of bounds");
    }

    byte[] byTmp = new byte[iSize];

    System.arraycopy(buffer, bytePointer - iSize, byTmp, 0, iSize);

    for (int i = 0; i < iSize; i++) {
      buffer[bytePointer - iSize + i] = byTmp[iSize - 1 - i];
    }
  }

  void preDecodeSwapByteOrder(int iSize) throws ASNException {
    if (bitPointer != 8) {
      throw new ASNException("The object must start on a byte");
    }

    byte[] byTmp = new byte[iSize];

    System.arraycopy(buffer, bytePointer, byTmp, 0, iSize);

    for (int i = 0; i < iSize; i++) {
      buffer[bytePointer + i] = byTmp[iSize - 1 - i];
    }
  }
}
