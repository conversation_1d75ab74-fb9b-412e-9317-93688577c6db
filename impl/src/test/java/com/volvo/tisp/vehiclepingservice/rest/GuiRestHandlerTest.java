package com.volvo.tisp.vehiclepingservice.rest;

import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.volvo.tisp.vehiclepingservice.metrics.LegacyMetricsReporter;

@ExtendWith(SpringExtension.class)
public class GuiRestHandlerTest {
  @InjectMocks
  private GuiRestHandler guiRestHandler;
  @Mock
  private LegacyMetricsReporter legacyMetricsReporter;

  @Test
  void guiRestHandlerConstructorTest() {
    Assertions.assertNotNull(new GuiRestHandler(Mockito.mock(LegacyMetricsReporter.class)));
  }

  @Test
  void isAuthenticatedForBeefyCallsMetricsAndReturnsCompletedFutureTest() {
    CompletableFuture<Void> result = guiRestHandler.isAuthenticatedForBeefy();

    Mockito.verify(legacyMetricsReporter).legacyGuiBeefyPermission();
    Assertions.assertTrue(result.isDone());
    Mockito.verifyNoMoreInteractions(legacyMetricsReporter);
  }

  @Test
  void isAuthenticatedForCustomReturnsNonNullCompletableFutureTest() {
    CompletableFuture<Void> result = guiRestHandler.isAuthenticatedForCustom();

    Assertions.assertTrue(result.isDone());
    Mockito.verify(legacyMetricsReporter).legacyGuiCustomPermission();
    Mockito.verifyNoMoreInteractions(legacyMetricsReporter);
  }
}
