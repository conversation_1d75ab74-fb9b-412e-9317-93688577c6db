package com.volvo.tisp.vehiclepingservice.compression;

import com.github.luben.zstd.Zstd;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class ZstdCompressionHandler implements CompressionHandler {
  @Override
  public byte[] compress(byte[] bytes) {
    Validate.notNull(bytes, "bytes");

    return Zstd.compress(bytes);
  }

  @Override
  public byte[] decompress(byte[] bytes) {
    Validate.notNull(bytes, "bytes");

    int decompressionBufferSize = (int) Zstd.getFrameContentSize(bytes);
    if (decompressionBufferSize == 0) {
      decompressionBufferSize = bytes.length * 25; // Assume compression ratio is never > 25
    }
    return Zstd.decompress(bytes, decompressionBufferSize);
  }
}
