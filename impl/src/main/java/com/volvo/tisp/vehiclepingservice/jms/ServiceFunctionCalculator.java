package com.volvo.tisp.vehiclepingservice.jms;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;

/**
 * Used for MtDispatchers "ServiceFunction" so VC's components can pick the right schema
 */
@Component
public class ServiceFunctionCalculator {
  public static final String PING_PREFIX = "ping";
  private static final String SAT_ONLY_POSTFIX = "-sat-only";
  private static final String SMS_ONLY_POSTFIX = "-sms-only";
  private static final String UDP_ONLY_POSTFIX = "-udp-only";
  private static final Logger logger = LoggerFactory.getLogger(ServiceFunctionCalculator.class);

  private final Channel defaultChannel;

  public ServiceFunctionCalculator() {
    this.defaultChannel = Channel.UDP;
  }

  private static String getPostfixString(Channel channel) {
    return switch (channel) {
      case UDP -> UDP_ONLY_POSTFIX;
      case SAT -> SAT_ONLY_POSTFIX;
      case SMS -> SMS_ONLY_POSTFIX;
    };
  }

  public String calculateServiceFunction(Channel channel) {
    StringBuilder sb = new StringBuilder();
    sb.append(PING_PREFIX);

    channel = (channel == null) ? defaultChannel : channel;
    sb.append(getPostfixString(channel));

    logger.debug(Retention.SHORT, "Service function: {}", sb);
    return sb.toString();
  }
}
