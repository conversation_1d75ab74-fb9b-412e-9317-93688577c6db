package com.volvo.tisp.vehiclepingservice.domain.v2;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.jms.MtDto;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;
import com.volvo.tisp.vehiclepingservice.util.TimeoutCalculator;
import com.volvo.tisp.vps.api.PingRequest;
import com.volvo.tisp.vps.api.TestService;
import com.wirelesscar.telematicunitservice.TelematicUnitService;

@ExtendWith(MockitoExtension.class)
class ReceiveMoPingSendMtPongStepV2Test {

  @Mock
  private MtMessagePublisher mtMessagePublisher;

  @Mock
  private PingV2Coder pingV2Coder;
  @InjectMocks
  private ReceiveMoPingSendMtPongStepV2 receiveMoPingSendMtPongStep;
  @Mock
  private TelematicUnitService telematicUnitService;
  @Mock
  private TimeoutCalculator timeoutCalculator;

  @Test
  void shouldCreateMtDtoWithCorrectValuesTest() {
    MoDtoV2 inputDto = new MoDtoV2();
    TestService testService = new TestService();
    PingRequest pingRequest = new PingRequest();
    pingRequest.setCorrelationId(UUID.randomUUID());
    testService.setPingRequest(pingRequest);
    inputDto.setDecoded(testService);
    inputDto.setVpi(TestUtils.VPI);
    inputDto.setReceivingChannel("CHANNEL1");
    inputDto.setCorrelationId("correlationId123");
    String system = "system";
    byte[] mockPayload = "testPayload".getBytes();

    Mockito.when(pingV2Coder.encodePong(Mockito.eq(inputDto), Mockito.isNull())).thenReturn(mockPayload);
    Mockito.when(timeoutCalculator.getDefaultTimeout(Mockito.any(Channel.class))).thenReturn(60000);
    Mockito.when(telematicUnitService.getSystem(TestUtils.VPI.toString())).thenReturn(CompletableFuture.completedFuture(system));
    Mockito.when(mtMessagePublisher.publishMtMessage(Mockito.any(MtDto.class))).thenReturn(CompletableFuture.completedFuture(null));

    CompletableFuture<Void> result = receiveMoPingSendMtPongStep.action(inputDto);

    Assertions.assertNotNull(result);
    Mockito.verify(pingV2Coder).encodePong(Mockito.eq(inputDto), Mockito.isNull());
    Mockito.verify(telematicUnitService).getSystem(inputDto.getVpi().toString());
    Mockito.verify(timeoutCalculator).getDefaultTimeout(Mockito.any(Channel.class));

    ArgumentCaptor<MtDto> mtDtoCaptor = ArgumentCaptor.forClass(MtDto.class);
    Mockito.verify(mtMessagePublisher).publishMtMessage(mtDtoCaptor.capture());
    MtDto mtDto = mtDtoCaptor.getValue();
    Assertions.assertEquals(2, mtDto.getServiceVersion());
    Assertions.assertEquals(system, mtDto.getSystem());
    Mockito.verifyNoMoreInteractions(mtMessagePublisher, pingV2Coder, timeoutCalculator, telematicUnitService);
  }
}

