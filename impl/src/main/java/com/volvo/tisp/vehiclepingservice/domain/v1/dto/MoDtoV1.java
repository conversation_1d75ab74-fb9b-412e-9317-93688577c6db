package com.volvo.tisp.vehiclepingservice.domain.v1.dto;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import java.util.Objects;

public class MoDtoV1 {
    private String correlationId;
    private DecodedV1 decoded;
    private String keyId;
    private long onBoardTimeStamp;
    private String receivingChannel;
    private long serverTimestamp;
    private String serviceAccessToken;
    private Vpi vpi;

    public String getCorrelationId() {
        return correlationId;
    }

    public DecodedV1 getDecoded() {
        return decoded;
    }

    public String getKeyId() {
        return keyId;
    }

    public long getOnBoardTimeStamp() {
        return onBoardTimeStamp;
    }

    public String getReceivingChannel() {
        return receivingChannel;
    }

    public long getServerTimestamp() {
        return serverTimestamp;
    }

    public String getServiceAccessToken() {
        return serviceAccessToken;
    }

    public Vpi getVpi() {
        return vpi;
    }

    public void setCorrelationId(String correlationId) {
        this.correlationId = correlationId;
    }

    public void setDecoded(DecodedV1 decoded) {
        this.decoded = decoded;
    }

    public void setKeyId(String keyId) {
        this.keyId = keyId;
    }

    public void setOnBoardTimeStamp(long onBoardTimeStamp) {
        this.onBoardTimeStamp = onBoardTimeStamp;
    }

    public void setReceivingChannel(String receivingChannel) {
        this.receivingChannel = receivingChannel;
    }

    public void setServerTimestamp(long serverTimestamp) {
        this.serverTimestamp = serverTimestamp;
    }

    public void setServiceAccessToken(String serviceAccessToken) {
        this.serviceAccessToken = serviceAccessToken;
    }

    public void setVpi(Vpi vpi) {
        this.vpi = vpi;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MoDtoV1 moDtoV1 = (MoDtoV1) o;
        return onBoardTimeStamp == moDtoV1.onBoardTimeStamp &&
               serverTimestamp == moDtoV1.serverTimestamp &&
               Objects.equals(correlationId, moDtoV1.correlationId) &&
               Objects.equals(decoded, moDtoV1.decoded) &&
               Objects.equals(keyId, moDtoV1.keyId) &&
               Objects.equals(receivingChannel, moDtoV1.receivingChannel) &&
               Objects.equals(serviceAccessToken, moDtoV1.serviceAccessToken) &&
               Objects.equals(vpi, moDtoV1.vpi);
    }

    @Override
    public int hashCode() {
        return Objects.hash(correlationId, decoded, keyId, onBoardTimeStamp, receivingChannel, serverTimestamp, serviceAccessToken, vpi);
    }

    @Override
    public String toString() {
        return "MoDtoV1{" +
               "correlationId='" + correlationId + '\'' +
               ", decoded=" + decoded +
               ", keyId='" + keyId + '\'' +
               ", onBoardTimeStamp=" + onBoardTimeStamp +
               ", receivingChannel='" + receivingChannel + '\'' +
               ", serverTimestamp=" + serverTimestamp +
               ", serviceAccessToken='" + serviceAccessToken + '\'' +
               ", vpi=" + vpi +
               '}';
    }
}
