package com.volvo.tisp.vehiclepingservice.conf;

import java.util.Arrays;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;

public class TestEnvironmentCondition implements Condition {
  private static final String[] TEST_ENV_MATCHES = new String[] {"local", "vagrant", "component-test"};

  @Override
  public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
    Config config = ConfigFactory.getConfig();
    String envId = config.getEnvironmentId();
    boolean matchEnv = Arrays.asList(TEST_ENV_MATCHES).contains(envId);
    return matchEnv || envId.startsWith("loadtest");
  }
}
