package com.volvo.tisp.vehiclepingservice.domain.exceptions;

import java.io.Serial;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Throw this if your MessagePublisher (SubscriptionRepository) returns 0 subscribers
 */
@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class NoSubscriberException extends RuntimeException {
  @Serial
  private static final long serialVersionUID = 1L;

  public NoSubscriberException() {
    super();
  }

  public NoSubscriberException(String msg) {
    super(msg);
  }

  public NoSubscriberException(String msg, Throwable t) {
    super(msg, t);
  }

  public NoSubscriberException(String publisher, String messageType) {
    super(
        "No subscriber for Publisher: '" + publisher + "' and MessageType: '" + messageType + "'");
  }
}
