package com.volvo.tisp.vehiclepingservice.integration.tests.v2;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import org.awaitility.Awaitility;
import org.springframework.stereotype.Component;
import org.springframework.test.web.reactive.server.WebTestClient;

import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.IdpMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.Idpm2mMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.JWKSClientTestMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.MtDispatcherMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.StateRepositoryRestMockExtended;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.TceMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.RestUtil;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.rest.model.PingToVehicleRequest;
import com.volvo.tisp.vehiclepingservice.rest.model.VehiclePingDataResponse;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;
import com.volvo.tisp.vps.api.PingResponse;


@Component
public class PingV2withPayloadBadChecksum {
  private final JWKSClientTestMock jWKSClientTestMock;
  private final MtDispatcherMock mtDispatcherMock;
  private final MtMessagePublisher mtMessagePublisher;
  private final PingEntityService pingEntityService;
  private final ServiceAccessTokenService serviceAccessTokenService;
  private final StateRepositoryRestMockExtended stateRepositoryRestMock;
  private final TceMock tceMock;
  private final WebTestClient webTestClient;

  public PingV2withPayloadBadChecksum(JWKSClientTestMock jWKSClientTestMock, MtDispatcherMock mtDispatcherMock, MtMessagePublisher mtMessagePublisher,
      PingEntityService pingEntityService, StateRepositoryRestMockExtended stateRepositoryRestMock, TceMock tceMock, WebTestClient webTestClient,
      ServiceAccessTokenService serviceAccessTokenService) {
    this.jWKSClientTestMock = jWKSClientTestMock;
    this.mtDispatcherMock = mtDispatcherMock;
    this.mtMessagePublisher = mtMessagePublisher;
    this.pingEntityService = pingEntityService;
    this.stateRepositoryRestMock = stateRepositoryRestMock;
    this.tceMock = tceMock;
    this.webTestClient = webTestClient;
    this.serviceAccessTokenService = serviceAccessTokenService;
  }

  private static void verifyDataApi(String vpi, VehiclePingDataResponse vehiclePingDataResponse) {
    assertEquals(vpi, vehiclePingDataResponse.getVpi());
    assertArrayEquals(TestUtil.PAYLOAD.getBytes(), vehiclePingDataResponse.getPayloadSent().getBytes());
    assertNull(vehiclePingDataResponse.getPayloadReceived());
    assertNotNull(vehiclePingDataResponse.getStartTime());
    assertNotNull(vehiclePingDataResponse.getStopTime());
    assertEquals(VehiclePingDataResponse.StatusEnum.FAILED, vehiclePingDataResponse.getStatus());
    assertNotNull(vehiclePingDataResponse.getErrorDescription());
  }

  public void test(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    mtMessagePublisher.setMtDispatcherFeature(mtDispatcher);
    serviceAccessTokenService.setServiceAccessTokenValidationEnabled(tokenValidation);

    if (mtDispatcher) {
      Idpm2mMock.mockCallToFetchServiceAccessToken("mh.w svc.256", "clientId");
    }
    String obsToken = null;
    if (tokenValidation) {
      obsToken = jWKSClientTestMock.setUpJwtToken();
    }

    String vpi = TestUtil.stubTusSubscription(2);

    stateRepositoryRestMock.stubPush(Instant.now().plus(3, ChronoUnit.MINUTES), 200);

    PingToVehicleRequest pingToVehicleRequest = TestUtil.createPingToVehicleRequestUdp(vpi, true, TestUtil.PAYLOAD);
    // REST api call
    String correlationId = String.valueOf(
        RestUtil.verifyPostHttpResponse(webTestClient, "/v2/ping", obsToken, pingToVehicleRequest, PingResponse.class)
            .getCorrelationId());

    PingEntity pingEntity = pingEntityService.findByCorrelationId(correlationId).get();
    TestUtil.verifyPingEntityPending(vpi, pingEntity);

    stateRepositoryRestMock.stubPop(correlationId, List.of(correlationId));

    // Respond with Pong
    if (mtDispatcher) {
      mtDispatcherMock.checkAndReplyPongV2(useMoRouter, obsToken, Channel.UDP, false);
    } else {
      tceMock.checkAndReplyPongV2(Channel.UDP, false);
    }

    // Poll DB data
    Awaitility.await()
        .atMost(Duration.of(10, ChronoUnit.SECONDS))
        .until(() -> pingEntityService.findByCorrelationId(correlationId).join().getStatus() != Status.PENDING);
    PingEntity pingEntityFinalDb = pingEntityService.findByCorrelationId(correlationId).join();

    // assert DB data
    TestUtil.verifyPingEntity(pingEntityFinalDb, vpi);

    // get REST data
    VehiclePingDataResponse vehiclePingDataResponse = RestUtil.verifyGetHttpResponse(webTestClient, "/v2/data/correlationId/" + correlationId, obsToken,
        VehiclePingDataResponse.class);

    // validate REST data
    verifyDataApi(vpi, vehiclePingDataResponse);

    if (mtDispatcher) {
      IdpMock.verifyIdpWiremockTokenInteractions(1);
    }
  }
}