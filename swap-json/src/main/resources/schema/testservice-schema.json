{"$schema": "https://json-schema.org/draft/2019-09/schema", "$id": "http://volvo.com/ocp/schemas/swap/256/2/1/testservice.json", "$defs": {"UUID": {"type": "string", "format": "uuid"}, "data": {"properties": {"checksum": {"type": "string", "description": "sha256 hash of payloadValue"}, "payloadValue": {"type": "string", "maxLength": 127000}}, "required": ["checksum", "payloadValue"]}, "error": {"properties": {"errorCode": {"type": "string", "enum": ["UNSUPPORTED_VERSION", "OTHER"]}, "errorDescription": {"type": "string"}}, "required": ["errorCode", "errorDescription"]}, "timestamp": {"type": "string", "format": "date-time"}, "PingRequest": {"type": "object", "properties": {"correlationId": {"$ref": "#/$defs/UUID", "description": "Used to correlate with the response message"}, "payload": {"$ref": "#/$defs/data"}, "copyPayloadInResponse": {"type": "boolean"}, "pingRequestCreated": {"$ref": "#/$defs/timestamp"}}, "required": ["correlationId", "pingRequestCreated"]}, "PingResponse": {"type": "object", "properties": {"correlationId": {"$ref": "#/$defs/UUID", "description": "Used for correlating a response with a request"}, "payload": {"$ref": "#/$defs/data"}, "status": {"type": "string", "enum": ["OK", "ERROR"]}, "error": {"$ref": "#/$defs/error"}, "timestamps": {"type": "object", "properties": {"pingRequestCreated": {"$ref": "#/$defs/timestamp"}, "pingRequestReceived": {"$ref": "#/$defs/timestamp"}, "pingResponseCreated": {"$ref": "#/$defs/timestamp"}}, "required": ["pingRequestCreated", "pingRequestReceived", "pingResponseCreated"]}}, "required": ["correlationId", "status", "timestamps"]}}, "title": "TestService", "type": "object", "properties": {"minorVersion": {"type": "integer"}, "pingRequest": {"description": "", "$ref": "#/$defs/PingRequest"}, "pingResponse": {"description": "", "$ref": "#/$defs/PingResponse"}}, "required": ["minorVersion"]}