package com.volvo.tisp.vehiclepingservice.domain.v1;

import java.time.Instant;
import java.util.concurrent.CompletableFuture;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;

public abstract class ReceiveStep {
  protected final PingEntityService pingEntityService;
  private final StateRepository stateRepository;

  protected ReceiveStep(PingEntityService pingEntityService, StateRepository stateRepository) {
    this.pingEntityService = pingEntityService;
    this.stateRepository = stateRepository;
  }

  CompletableFuture<PingEntity> validateAndSave(PingEntity pingEntity, byte[] payload) {
    pingEntity.setStatus(Status.SUCCESS);
    pingEntity.setStopTimeInMillis(Instant.now().toEpochMilli());
    int durationInSeconds = (int) (pingEntity.getStopTimeInMillis() - pingEntity.getStartTimeInMillis()) / 1000;
    pingEntity.setDuration(durationInSeconds);

    if (payload != null) {
      pingEntity.setPayloadReceived(payload);
    }

    return pingEntityService.upsert(pingEntity)
        .toCompletableFuture()
        .thenCompose(savedPingEntity -> stateRepository.pop(savedPingEntity.getCorrelationId()).thenApply(states -> savedPingEntity));
  }
}
