package com.volvo.tisp.vehiclepingservice.domain.v2;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.vehiclepingservice.compression.CompressionHandler;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.util.ChecksumCalculator;
import com.volvo.tisp.vps.api.Data;
import com.volvo.tisp.vps.api.Error;
import com.volvo.tisp.vps.api.Error.ErrorCode;
import com.volvo.tisp.vps.api.PingRequest;
import com.volvo.tisp.vps.api.PingResponse;
import com.volvo.tisp.vps.api.PingResponse.Status;
import com.volvo.tisp.vps.api.TestService;
import com.volvo.tisp.vps.api.Timestamps;

public class PingV2Coder {
  private static final Logger logger = LoggerFactory.getLogger(PingV2Coder.class);

  private final CompressionHandler compressionHandler;
  private final ObjectMapper objectMapper;

  public PingV2Coder(CompressionHandler compressionHandler, ObjectMapper objectMapper) {
    this.compressionHandler = compressionHandler;
    this.objectMapper = objectMapper;
  }

  public TestService decode(byte[] payload) {
    byte[] decompressedPayload = compressionHandler.decompress(payload);

    String decodedMessage = new String(decompressedPayload, StandardCharsets.UTF_8);
    try {
      return objectMapper.readValue(decodedMessage, TestService.class);
    } catch (JsonProcessingException e) {
      String msg = "Could not decode V2 MoMessage";
      logger.error(msg, e);
      throw new RuntimeException(e);
    }
  }

  public byte[] encodePing(UUID correlationID, String payload, Boolean copyPayload) {
    TestService ts = new TestService();
    ts.setMinorVersion(1);
    PingRequest request = new PingRequest();
    ts.setPingRequest(request);
    request.setCorrelationId(correlationID);
    request.setPingRequestCreated(ZonedDateTime.now());
    if (StringUtils.hasLength(payload)) {
      Data data = new Data();
      data.setChecksum(ChecksumCalculator.getSHA256Hash(payload));
      data.setPayloadValue(payload);
      request.setPayload(data);
      request.setCopyPayloadInResponse(copyPayload);
    }

    String mtMessage;
    try {
      mtMessage = objectMapper.writeValueAsString(ts);
    } catch (JsonProcessingException e) {
      String msg = "Could not encode V2 Ping to Json";
      logger.error(msg, e);
      throw new RuntimeException(msg, e);
    }
    logger.debug(Retention.SHORT, "RAW payload JSON: {}", mtMessage);
    return compressionHandler.compress(mtMessage.getBytes(StandardCharsets.UTF_8));
  }

  public byte[] encodePong(MoDtoV2 moDtoV2, String errorDescription) {
    PingRequest pingRequest = moDtoV2.getDecoded().getPingRequest();
    TestService testService = new TestService();
    testService.setMinorVersion(1);
    PingResponse response = new PingResponse();

    Timestamps timestamps = new Timestamps()
        .withPingRequestCreated(pingRequest.getPingRequestCreated())
        .withPingRequestReceived(ZonedDateTime.ofInstant(Instant.ofEpochMilli(moDtoV2.getServerTimestamp()), ZoneId.systemDefault()))
        .withPingResponseCreated(ZonedDateTime.now());
    response.setTimestamps(timestamps);

    response.setCorrelationId(moDtoV2.getDecoded().getPingRequest().getCorrelationId());
    response.setStatus(Status.OK);

    if (StringUtils.hasLength(errorDescription)) {
      Error error = new Error();
      error.setErrorCode(ErrorCode.OTHER);
      error.setErrorDescription(errorDescription);
      response.setError(error);
      response.setStatus(Status.ERROR);
    } else if (pingRequest.getCopyPayloadInResponse() != null && pingRequest.getCopyPayloadInResponse() && pingRequest.getPayload() != null) {
      if (!isValid(pingRequest.getPayload())) {
        Error error = new Error();
        error.setErrorCode(ErrorCode.OTHER);
        error.setErrorDescription("Payload in request did not match the Checksum for the payload");
        response.setError(error);
        response.setStatus(Status.ERROR);
        logger.warn("Checksum was not valid in Ping from Vehicle V2, vpi: {}", moDtoV2.getVpi());
      } else {
        response.setPayload(pingRequest.getPayload());
      }
    }

    testService.setPingResponse(response);
    String mtMessage;
    try {
      mtMessage = objectMapper.writeValueAsString(testService);
    } catch (JsonProcessingException e) {
      String msg = "Could not encode V2 PingResponse to Json";
      logger.error(msg, e);
      throw new RuntimeException(msg, e);
    }

    return compressionHandler.compress(mtMessage.getBytes(StandardCharsets.UTF_8));
  }

  public boolean isValid(Data data) {
    return data.getChecksum().equals(ChecksumCalculator.getSHA256Hash(data.getPayloadValue()));
  }
}
