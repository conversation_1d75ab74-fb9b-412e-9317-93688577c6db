package com.volvo.tisp.vehiclepingservice.swap.common;

import org.jdom2.Element;

/**
 * Title: ASN ConstrainedValue base class.
 *
 * <p>Description: Part of ASN
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ASNConstrainedValue extends ASNValue {

  static final int FIXED_SIZE = 1;
  static final int REFERENCE_SIZE = 3;
  static final int UNBOUNDED_COUNT = 4;
  static final int UNBOUNDED_HIGH = 6;
  static final int UNBOUNDED_INTEGER = 5;
  static final int UNBOUNDED_LOW = 7;
  static final int UNKNOWN = 0;
  static final int VARIABLE_SIZE = 2;
  /**
   *
   */
  private static final long serialVersionUID = 1L;
  protected int bits = 0;
  protected int constrainType = UNKNOWN;
  protected boolean extendeble = false;
  protected long highBoundary = 0;
  protected long lowBoundary = 0;
  private boolean extended = false;

  public ASNConstrainedValue() {
  }

  public void SetConstraints(ASNConstrainedValue[] list) {

    // Start with a copy of the first union
    if (list.length > 0) {
      this.constrainType = list[0].constrainType;
      this.lowBoundary = list[0].lowBoundary;
      this.highBoundary = list[0].highBoundary;
      this.bits = list[0].bits;
    }
    for (ASNConstrainedValue entry : list) {

      if (this.constrainType == UNBOUNDED_INTEGER || entry.constrainType == UNBOUNDED_INTEGER) {
        setUnboundConstraint();
        return;
      }

      // Check if "half unbounded" -> "full unbounded"
      if (this.constrainType == UNBOUNDED_LOW && entry.constrainType == UNBOUNDED_HIGH) {
        setUnboundConstraint();
        return;
      }

      // Check if "half unbounded" -> "full unbounded"
      if (this.constrainType == UNBOUNDED_HIGH && entry.constrainType == UNBOUNDED_LOW) {
        setUnboundConstraint();
        return;
      }

      // Check if "bounded" -> "low unbounded"
      if (this.constrainType != UNBOUNDED_LOW && entry.constrainType == UNBOUNDED_LOW) {
        setHighBoundaryConstraint(entry.highBoundary);
        return;
      }

      // Check if "bounded" -> "high unbounded"
      if (this.constrainType != UNBOUNDED_HIGH && entry.constrainType == UNBOUNDED_HIGH) {
        setLowBoundaryConstraint(entry.lowBoundary);
        return;
      }

      if (entry.lowBoundary < this.lowBoundary)
        this.lowBoundary = entry.lowBoundary;

      if (entry.highBoundary > this.highBoundary)
        this.highBoundary = entry.highBoundary;
    }
  }

  public ASNValue createObject() throws ASNException {
    return new ASNConstrainedValue();
  }

  public void decode(PERStream stream) throws ASNException {
  }

  public void decode(Element oElement) throws ASNException {
  }

  public void encode(PERStream stream) throws ASNException {
  }

  public void encode(Element oElement) throws ASNException {
  }

  public long encodedSize() {
    return 0;
  }

  public boolean isExtendeble() {
    return extendeble;
  }

  public boolean isExtended() {
    return extended;
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    constrainType = in.readInt();
    bits = in.readInt();
    lowBoundary = in.readLong();
    highBoundary = in.readLong();
  }

  public void setExtendeble(boolean extendeble) {
    this.extendeble = extendeble;
  }

  public void setExtended(boolean extended) {
    this.extended = extended;
  }

  public void setFixedConstraint(long highBoundary) {
    constrainType = FIXED_SIZE;
    // Only used by BitString as a fixed size of bits!!!
    this.bits = (int) highBoundary;
    this.lowBoundary = 0L;
    this.highBoundary = highBoundary - 1L;
  }

  public void setFixedConstraint(long lowBoundary, long highBoundary) {
    constrainType = VARIABLE_SIZE;
    this.lowBoundary = lowBoundary;
    this.highBoundary = highBoundary;
  }

  public void setFixedSizeConstraint(int highBoundary) {
    constrainType = FIXED_SIZE;
    // Only used by BitString as a fixed size of bits!!!
    this.bits = highBoundary;
    this.lowBoundary = 0L;
    this.highBoundary = highBoundary - 1L;
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    out.writeInt(constrainType);
    out.writeInt(bits);
    out.writeLong(lowBoundary);
    out.writeLong(highBoundary);
  }

  long getHighBoundary() {
    return highBoundary;
  }

  long getLowBoundary() {
    return lowBoundary;
  }

  void setFixedSizeConstraint(int lowBoundary, int highBoundary) {
    constrainType = VARIABLE_SIZE;
    this.lowBoundary = lowBoundary;
    this.highBoundary = highBoundary;
  }

  void setHighBoundaryConstraint(long highBoundary) {
    constrainType = UNBOUNDED_LOW;
    this.highBoundary = highBoundary;
  }

  void setLowBoundaryConstraint(long lowBoundary) {
    constrainType = UNBOUNDED_HIGH;
    this.lowBoundary = lowBoundary;
  }

  void setUnboundConstraint() {
    this.constrainType = UNBOUNDED_INTEGER;
    this.highBoundary = this.lowBoundary = 0;
  }
}
