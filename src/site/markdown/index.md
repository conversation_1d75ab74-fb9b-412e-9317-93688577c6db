# Staterepository

// TODO // DOJO-OPTIONAL-AT-STEP-1 // Put the relevant info for your component in this file. (Links below should NOT require changes.)

The StateRepository allows a component to temporary push a state (push) to be later retrieved (pop).  If the state has not been retrieved before a set timeout a callback handler will be called.
## API Documentation
1. [REST API Documentation](api-docs/rest-api-index.html)
1. [Swagger UI JSON](api-docs/json/rest-api.json)

## Important reports
1. [Unit Tests](surefire-report.html)
1. [Component Tests](failsafe-report.html)
1. [Load Tests](load-test-report.html)
1. Findbugs reports can be found in each module
