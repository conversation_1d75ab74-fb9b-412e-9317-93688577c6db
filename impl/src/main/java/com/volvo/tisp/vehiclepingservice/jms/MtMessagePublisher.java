package com.volvo.tisp.vehiclepingservice.jms;

import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.NoSubscriberException;
import com.volvo.tisp.vehiclepingservice.metrics.MtMessagePublisherMetricReporter;

@Component
public class MtMessagePublisher {
  private static final Logger logger = LoggerFactory.getLogger(MtMessagePublisher.class);

  private boolean mtDispatcherFeature;
  private final MtDispatcherMessagePublisher mtDispatcherMessagePublisher;
  private final MtMessagePublisherMetricReporter mtMessagePublisherMetricReporter;
  private final TceMessagePublisher tceMessagePublisher;

  public MtMessagePublisher(@Value("${mtdispatcher.feature:false}") boolean mtDispatcherFeature, MtDispatcherMessagePublisher mtDispatcherMessagePublisher,
      TceMessagePublisher tceMessagePublisher, MtMessagePublisherMetricReporter mtMessagePublisherMetricReporter) {
    this.mtDispatcherFeature = mtDispatcherFeature;
    logger.info("MT Dispatcher Feature: {}", mtDispatcherFeature);
    this.mtDispatcherMessagePublisher = mtDispatcherMessagePublisher;
    this.tceMessagePublisher = tceMessagePublisher;
    this.mtMessagePublisherMetricReporter = mtMessagePublisherMetricReporter;
  }

  public boolean getMtDispatcherFeature() {
    return this.mtDispatcherFeature;
  }

  public CompletableFuture<Void> publishMtMessage(MtDto dto) {
    // ugly fix to support TGW on TGWMTS and T3 on MtDispatcher at the same time
    // No TGW supports V2 yet
    // TODO: remove when MtDispatcher supports TGW and T3 in Prod
    if (mtDispatcherFeature || dto.getServiceVersion() == 2) {
      logger.debug(Retention.SHORT, "Publishing to MT Dispatcher");
      return mtDispatcherMessagePublisher.publish(dto);
    }
    // regular / legacy TCE (tgw-mos / tgw-mt / mopers / mtschd)
    logger.debug(Retention.SHORT, "Publishing to TCE");

    CompletableFuture<Void> future = new CompletableFuture<>();

    tceMessagePublisher
        .publish(dto)
        .thenAccept(subscribers -> {
          if (subscribers == 0) {
            future.completeExceptionally(new NoSubscriberException("No subscribers for TCE_MT_MESSAGE 2.0 for Ping/Beefy"));
            return;
          }
          mtMessagePublisherMetricReporter.onMtPublisherSuccess();
          logger.debug(Retention.SHORT, "Published message (TCE) to {} subscribers, vpi: {}, corrId: {}, serviceVersion: {}", subscribers, dto.getVpi(),
              dto.getCorrelationId(), dto.getServiceVersion());

          future.complete(null);
        })
        .exceptionally(throwable -> {
          mtMessagePublisherMetricReporter.onMtPublisherFailure();
          future.completeExceptionally(throwable);
          return null;
        });

    return future;
  }

  // TODO: For Testing, remove after featuretoggle removal
  public synchronized void setMtDispatcherFeature(boolean mtDispatcherFeature) {
    this.mtDispatcherFeature = mtDispatcherFeature;
    logger.debug(Retention.SHORT, "mtDispatcherFeature = {}", mtDispatcherFeature);
  }
}
