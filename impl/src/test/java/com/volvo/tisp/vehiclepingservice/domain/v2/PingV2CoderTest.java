package com.volvo.tisp.vehiclepingservice.domain.v2;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.vehiclepingservice.compression.CompressionHandler;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vps.api.PingRequest;
import com.volvo.tisp.vps.api.TestService;

@ExtendWith(MockitoExtension.class)
class PingV2CoderTest {
  @Mock
  private CompressionHandler compressionHandler;
  @Mock
  private ObjectMapper objectMapper;
  @InjectMocks
  private PingV2Coder pingV2Coder;

  @Test
  void decodeWithValidPayloadShouldReturnTestServiceTest() throws JsonProcessingException {
    // Arrange
    TestService expectedTestService = new TestService();
    byte[] compressedPayload = "compressed".getBytes();

    byte[] decompressedPayload = new ObjectMapper().writeValueAsString(expectedTestService).getBytes();

    Mockito.when(compressionHandler.decompress(compressedPayload)).thenReturn(decompressedPayload);
    Mockito.when(objectMapper.readValue(new String(decompressedPayload, StandardCharsets.UTF_8), TestService.class)).thenReturn(expectedTestService);

    TestService result = pingV2Coder.decode(compressedPayload);

    Assertions.assertNotNull(result);
    Assertions.assertEquals(expectedTestService, result);
    Mockito.verify(compressionHandler).decompress(compressedPayload);
    Mockito.verify(objectMapper).readValue(new String(decompressedPayload, StandardCharsets.UTF_8), TestService.class);
    Mockito.verifyNoMoreInteractions(compressionHandler, objectMapper);
  }

  @Test
  void decodeWithValidPayloadShouldThrowErrorTest() throws JsonProcessingException {
    // Arrange
    TestService expectedTestService = new TestService();
    byte[] compressedPayload = "compressed".getBytes();

    byte[] decompressedPayload = new ObjectMapper().writeValueAsString(expectedTestService).getBytes();

    Mockito.when(compressionHandler.decompress(compressedPayload)).thenReturn(decompressedPayload);
    Mockito.when(objectMapper.readValue(new String(decompressedPayload, StandardCharsets.UTF_8), TestService.class)).thenThrow(JsonProcessingException.class);

    Assertions.assertThrows(RuntimeException.class, () -> pingV2Coder.decode(compressedPayload), "Could not decode V2 MoMessage");
    Mockito.verify(compressionHandler).decompress(compressedPayload);
    Mockito.verify(objectMapper).readValue(new String(decompressedPayload, StandardCharsets.UTF_8), TestService.class);
    Mockito.verifyNoMoreInteractions(compressionHandler, objectMapper);
  }

  @Test
  void encodePingWithValidPayloadShouldReturnCompressedBytesTest() throws JsonProcessingException {
    UUID correlationId = UUID.randomUUID();
    String payload = "test payload";
    Boolean copyPayload = true;
    String deserializedValue = "deserialized";
    byte[] compressedBytes = "compressed".getBytes();

    Mockito.when(objectMapper.writeValueAsString(Mockito.any(TestService.class))).thenReturn(deserializedValue);
    Mockito.when(compressionHandler.compress(deserializedValue.getBytes())).thenReturn(compressedBytes);

    byte[] result = pingV2Coder.encodePing(correlationId, payload, copyPayload);

    Assertions.assertNotNull(result);
    Assertions.assertEquals(compressedBytes, result);
    Mockito.verify(compressionHandler).compress(Mockito.any());
    Mockito.verify(objectMapper).writeValueAsString(Mockito.any(TestService.class));
    Mockito.verifyNoMoreInteractions(compressionHandler, objectMapper);
  }

  @Test
  void encodePingWithValidPayloadShouldThrowErrorTest() throws JsonProcessingException {
    UUID correlationId = UUID.randomUUID();
    String payload = "test payload";
    Boolean copyPayload = true;

    Mockito.when(objectMapper.writeValueAsString(Mockito.any(TestService.class))).thenThrow(JsonProcessingException.class);

    Assertions.assertThrows(RuntimeException.class, () -> pingV2Coder.encodePing(correlationId, payload, copyPayload), "Could not encode V2 Ping to Json");
    Mockito.verifyNoMoreInteractions(objectMapper);
    Mockito.verifyNoInteractions(compressionHandler);
  }

  @Test
  void encodePongWithValidPayloadShouldReturnCompressedBytesTest() throws Exception {
    MoDtoV2 moDtoV2 = new MoDtoV2();
    TestService testService = new TestService();
    PingRequest pingRequest = new PingRequest();
    testService.setPingRequest(pingRequest);
    moDtoV2.setDecoded(testService);
    String responsePayload = "response payload";
    byte[] compressedBytes = "compressed".getBytes();

    Mockito.when(objectMapper.writeValueAsString(Mockito.any(TestService.class))).thenReturn(responsePayload);
    Mockito.when(compressionHandler.compress(responsePayload.getBytes())).thenReturn(compressedBytes);

    byte[] result = pingV2Coder.encodePong(moDtoV2, responsePayload);

    Assertions.assertEquals(compressedBytes, result);
    Mockito.verify(compressionHandler).compress(responsePayload.getBytes());
    Mockito.verify(objectMapper).writeValueAsString(Mockito.any(TestService.class));
    Mockito.verifyNoMoreInteractions(compressionHandler, objectMapper);
  }

  @Test
  void encodePongWhenJsonProcessingFailsShouldThrowRuntimeExceptionTest() throws Exception {
    MoDtoV2 moDtoV2 = new MoDtoV2();
    TestService testService = new TestService();
    PingRequest pingRequest = new PingRequest();
    pingRequest.setCorrelationId(UUID.randomUUID());
    testService.setPingRequest(pingRequest);
    moDtoV2.setDecoded(testService);
    String responsePayload = "response";

    Mockito.when(objectMapper.writeValueAsString(Mockito.any(TestService.class))).thenThrow(JsonProcessingException.class);

    Assertions.assertThrows(RuntimeException.class, () -> pingV2Coder.encodePong(moDtoV2, responsePayload), "Could not encode V2 PingResponse to Json");
  }

  @Test
  void encodePongShouldSetCorrectMinorVersionTest() throws Exception {
    MoDtoV2 moDtoV2 = new MoDtoV2();
    TestService testService = new TestService();
    PingRequest pingRequest = new PingRequest();
    pingRequest.setCorrelationId(UUID.randomUUID());
    testService.setPingRequest(pingRequest);
    moDtoV2.setDecoded(testService);
    String responsePayload = "response";
    byte[] compressedBytes = "compressed".getBytes();

    Mockito.when(objectMapper.writeValueAsString(Mockito.any(TestService.class))).thenReturn(responsePayload);
    Mockito.when(compressionHandler.compress(Mockito.any())).thenReturn(compressedBytes);

    pingV2Coder.encodePong(moDtoV2, responsePayload);

    Mockito.verify(objectMapper).writeValueAsString(Mockito.argThat(service -> {
      TestService ts = (TestService) service;
      return ts.getMinorVersion() == 1 && ts.getPingResponse().getCorrelationId().equals(pingRequest.getCorrelationId());
    }));
  }
}

