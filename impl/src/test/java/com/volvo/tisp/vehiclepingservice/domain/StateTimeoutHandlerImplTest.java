package com.volvo.tisp.vehiclepingservice.domain;

import java.time.Instant;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.junit.jupiter.api.Test;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;

@ExtendWith(MockitoExtension.class)
class StateTimeoutHandlerImplTest {

  @Mock
  private PingEntityService pingEntityService;
  @InjectMocks
  private StateTimeoutHandlerImpl stateTimeoutHandler;

  @Test
  void handleNotFoundTest() {
    String reference = "reference";
    Mockito.when(pingEntityService.findByCorrelationId(reference)).thenReturn(CompletableFuture.completedFuture(null));

    stateTimeoutHandler.handle(reference, null);

    Mockito.verify(pingEntityService).findByCorrelationId(reference);
    Mockito.verifyNoMoreInteractions(pingEntityService);
  }

  @Test
  void handleSuccessfulTest() {
    String reference = "reference";
    PingEntity pingEntity = new PingEntity();
    pingEntity.setCorrelationId(reference);
    pingEntity.setStartTimeInMillis(Instant.now().minusSeconds(1).toEpochMilli());

    Mockito.when(pingEntityService.findByCorrelationId(reference)).thenReturn(CompletableFuture.completedFuture(pingEntity));

    stateTimeoutHandler.handle(reference, null);

    Mockito.verify(pingEntityService).findByCorrelationId(reference);
    Mockito.verify(pingEntityService)
        .upsert(Mockito.argThat(entity -> entity.getStatus() == Status.TIMEOUT && entity.getStopTimeInMillis() != null && entity.getDuration() > 0));
    Mockito.verifyNoMoreInteractions(pingEntityService);
  }
}
