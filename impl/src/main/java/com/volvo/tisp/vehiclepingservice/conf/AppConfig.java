package com.volvo.tisp.vehiclepingservice.conf;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.EnableReactiveMongoAuditing;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableReactiveMongoRepositories;
import org.springframework.security.oauth2.core.endpoint.DefaultMapOAuth2AccessTokenResponseConverter;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.mongodb.client.MongoDatabase;
import com.volvo.tisp.idpm2m.client.JWKSClient;
import com.volvo.tisp.idpm2m.client.config.JWKSClientProperties;
import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.staterepository.StateTimeoutHandler;
import com.volvo.tisp.vehiclepingservice.compression.CompressionHandler;
import com.volvo.tisp.vehiclepingservice.compression.ZstdCompressionHandler;
import com.volvo.tisp.vehiclepingservice.domain.v2.PingV2Coder;
import com.volvo.tisp.vehiclepingservice.metrics.ServiceAccessTokenMetricReporter;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenClient;
import com.volvo.tisp.vehiclepingservice.util.TokenFetcher;
import com.wirelesscar.telematicunitservice.TelematicUnitService;
import com.wirelesscar.telematicunitservice._1_0.TelematicUnitServiceImpl;

@SpringBootApplication
@Configuration
@ComponentScan(basePackages = {"com.volvo.tisp.vehiclepingservice", "com.volvo.tisp.idpm2m.client.config"})
@EnableReactiveMongoRepositories(basePackages = "com.volvo.tisp.vehiclepingservice.database.repository")
@EnableReactiveMongoAuditing
@EnableAutoConfiguration
public class AppConfig {
  private static final Logger LOG = LoggerFactory.getLogger(AppConfig.class);
  private static final String SERVICE_ENDPOINT_NAME = "idpm2m";

  public static void main(String[] args) {
    SpringApplication.run(AppConfig.class, args);
  }

  @Bean
  public CompressionHandler compressionHandler() {
    return new ZstdCompressionHandler();
  }

  @Bean
  public JWKSClient jwksClient(JWKSClientProperties jwksClientProperties) {
    return Optional.ofNullable(jwksClientProperties)
        .filter(properties -> properties.s3Values() != null)
        .map(properties -> JWKSClient.builder(properties).newT3Client().build())
        .orElse(null);
  }

  @Bean
  public MongoDatabase mongoDatabase(MongoTemplate mongoTemplate) {
    return mongoTemplate.getDb();
  }

  @Bean
  public PingV2Coder pingV2Coder(CompressionHandler compressionHandler) {
    return new PingV2Coder(compressionHandler, new ObjectMapper().registerModule(new JavaTimeModule()));
  }

  @Bean
  public ServiceAccessTokenClient serviceAccessTokenClient(TokenFetcher tokenFetcher, ServiceAccessTokenMetricReporter serviceAccessTokenMetricReporter) {
    return new ServiceAccessTokenClient(tokenFetcher, serviceAccessTokenMetricReporter);
  }

  @Bean
  public StateRepository stateRepository(final StateRepository.Builder builder, StateTimeoutHandler stateTimeoutHandler) {
    return builder.timeoutHandler(stateTimeoutHandler).build();
  }

  @Bean
  public TokenFetcher tokenFetcher(WebClient webClient) {
    return new TokenFetcher(webClient, new DefaultMapOAuth2AccessTokenResponseConverter());
  }

  @Bean
  public TelematicUnitService tusClient(WebClient.Builder builder) {
    return new TelematicUnitServiceImpl(builder);
  }

  @Bean
  public WebClient webClient(WebClient.Builder webClientBuilder) {
    return webClientBuilder
        .baseUrl("https://" + SERVICE_ENDPOINT_NAME)
        .filters(exchangeFilterFunctions -> exchangeFilterFunctions.add(removeNonBasicFromAuthenticationHeader()))
        .build();
  }

  /*
  Bearer token could be presents if clientToken or accessToken is not blank
  https://git.vgt.volvo.com/plugins/gitiles/vgt/tisp/tisp-framework/+/refs/heads/main/tisp-framework/src/main/java/com/volvo/tisp/framework/web/filter
  /TispContextFilter.java?autodive=0%2F%2F%2F%2F%2F%2F%2F
  */
  private ExchangeFilterFunction removeNonBasicFromAuthenticationHeader() {
    return (request, next) -> {
      ClientRequest.Builder builder = ClientRequest.from(request);
      List<String> values = request.headers().getValuesAsList(org.springframework.http.HttpHeaders.AUTHORIZATION);
      Optional<String> basicAuthenticationOptional = values.stream().filter(value -> value.startsWith("Basic ")).findFirst();
      basicAuthenticationOptional.ifPresentOrElse(basicAuthentication -> {
        if (values.size() > 1) {
          builder.headers(httpHeaders -> httpHeaders.remove(org.springframework.http.HttpHeaders.AUTHORIZATION));
          builder.headers(httpHeaders -> httpHeaders.set(org.springframework.http.HttpHeaders.AUTHORIZATION, basicAuthentication));
        }
      }, () -> {
        // TODO: should an exception be thrown here
        LOG.warn("no BasicAuthentication is found in the http header");
      });
      return next.exchange(builder.build());
    };
  }
}
