#!/bin/python

import sys
import os

rootdir = sys.argv[1]

print rootdir

for subdir, dirs, files in os.walk(rootdir):
    for file in files:
        f = open(os.path.join(subdir,file), "r")
        contents = f.readlines()
        f.close()

        contents.insert(7, "import com.volvo.tisp.vehiclepingservice.swap.common.*;")

        f = open(os.path.join(subdir,file), "w")
        f.writelines(contents)
        f.close()
