package com.volvo.tisp.vehiclepingservice.domain.v1;

import org.springframework.stereotype.Component;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;

@Component
public class SendMtPingStepV1 extends SendMtStep {

  private final PingCoder pingCoder;

  public SendMtPingStepV1(PingCoder pingCoder, MtMessagePublisher mtMessagePublisher, StateRepository stateRepository) {
    super(mtMessagePublisher, stateRepository);
    this.pingCoder = pingCoder;
  }

  @Override
  protected byte[] getPayload(RequestData requestData, long swapCorrelationId) {
    return pingCoder.encodePing(swapCorrelationId);
  }
}
