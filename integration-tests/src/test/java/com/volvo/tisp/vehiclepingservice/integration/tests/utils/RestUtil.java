package com.volvo.tisp.vehiclepingservice.integration.tests.utils;

import org.junit.jupiter.api.Assertions;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.EntityExchangeResult;
import org.springframework.test.web.reactive.server.WebTestClient;

import com.volvo.tisp.vehiclepingservice.rest.model.PingToVehicleRequest;

public final class RestUtil {
  private RestUtil() {
    throw new IllegalArgumentException();
  }

  public static <T> T verifyGetHttpResponse(WebTestClient webTestClient, String url, String token, Class<T> responseType) {
    EntityExchangeResult<T> exchangeResult = verifyGetOkHttpResponse(webTestClient, url, token, responseType);

    Assertions.assertNotNull(exchangeResult.getResponseBody());

    return exchangeResult.getResponseBody();
  }

  public static <T> T verifyGetHttpResponse(WebTestClient webTestClient, String url, Class<T> responseType) {
    EntityExchangeResult<T> exchangeResult = verifyGetOkHttpResponse(webTestClient, url, responseType);

    T responseBody = exchangeResult.getResponseBody();
    Assertions.assertNotNull(exchangeResult.getResponseBody());

    return exchangeResult.getResponseBody();
  }

  public static <T> T verifyPostHttpResponse(WebTestClient webTestClient, String url, String token, PingToVehicleRequest requestBody, Class<T> responseType) {
    EntityExchangeResult<T> exchangeResult = verifyPostOkHttpResponse(webTestClient, url, token, requestBody, responseType);

    Assertions.assertNotNull(exchangeResult.getResponseBody());

    return exchangeResult.getResponseBody();
  }

  private static <T> EntityExchangeResult<T> verifyGetOkHttpResponse(WebTestClient webTestClient, String url, String token, Class<T> responseType) {
    WebTestClient.ResponseSpec exchange = webTestClient.get()
        .uri(url)
        .header("Authorization", "Bearer " + token)
        .exchange().expectStatus().is2xxSuccessful();
    return exchange.expectBody(responseType).returnResult();
  }

  private static <T> EntityExchangeResult<T> verifyGetOkHttpResponse(WebTestClient webTestClient, String url, Class<T> responseType) {
    WebTestClient.ResponseSpec exchange = webTestClient.get()
        .uri(url)
        .exchange().expectStatus().is2xxSuccessful();
    return exchange.expectBody(responseType).returnResult();
  }

  private static <T> EntityExchangeResult<T> verifyPostOkHttpResponse(WebTestClient webTestClient, String url, String token, PingToVehicleRequest requestBody,
      Class<T> responseType) {
    WebTestClient.ResponseSpec exchange = webTestClient.post()
        .uri(url)
        .header("Authorization", "Bearer " + token)
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(requestBody)
        .exchange()
        .expectStatus()
        .is2xxSuccessful()
        .expectHeader()
        .contentType(MediaType.APPLICATION_JSON);
    return exchange.expectBody(responseType).returnResult();
  }
}
