import * as React from "react";
import {
    BaseColours,
    createErrorScreen,
    createLoadingScreenBuildingCogs,
    createLoadingScreenDrivingTruck,
    CSSService,
    STCTextField,
    STCToolBase,
    STCToolProps,
    STCToolState
} from "support-tool-components";
import {RequestService} from "../services/RequestService";
import {Button, Container, Grid, SemanticTRANSITIONS, Transition} from "semantic-ui-react";
import {faAngleLeft, faAngleRight, faHome, faTruck} from "@fortawesome/free-solid-svg-icons";
import {FontAwesomeIcon} from "@fortawesome/react-fontawesome";

interface SRCState extends STCToolState {
    vpi: string,
    correlationId: string,
    formHeight?: number,
    failReason?: string,
    currentStep: VehiclePingToolSteps,
    allowNext?: boolean,
    pingResult?: any,
}

interface SRCProps extends STCToolProps {
    vpi?: string,
    auth?: boolean,
    embedded?: boolean
}

export enum VehiclePingToolSteps {
    FORM, SENDING_REQUEST, WAITING_FOR_REPLY, SUCCESS, ERROR, TRANSITION
}

export interface PingRequestResult {
    success: boolean,
    message: string,
    correlationId?: string
}

export class VehiclePingTool extends STCToolBase<SRCProps, SRCState> {
    transitionAnimation: SemanticTRANSITIONS = "vertical flip";
    animationDelay = 500;
    pingPollPeriod: number = 2000;
    interval: any = undefined;

    constructor(props: SRCProps) {
        super(props);
        this.state = {
            vpi: "",
            currentStep: VehiclePingToolSteps.FORM,
            visible: this.props.initVisible == undefined ? true : this.props.initVisible,
            correlationId: ""
        };
    }

    private async onSubmit(vpi: string): Promise<PingRequestResult> {
        let result: PingRequestResult;
        await RequestService.ping(vpi)
            .then((response) => {
                console.log(response);
                result = {
                    success: true,
                    message: "Done",
                    correlationId: response
                }
            }).catch((error) => {
                result = {
                    success: false,
                    message: error
                }
            });

        return new Promise<PingRequestResult>((resolve) => {
            resolve(result);
        });
    }

    previousStep() {
        this.setState({currentStep: VehiclePingToolSteps.FORM})
    }

    async getPingResult() {
        await RequestService.getPingResult(this.state.correlationId)
            .then((response) => {
                if (response.stopTime != undefined) {
                    if (response.success != undefined && response.success == false) {

                        let startTime = new Date(response.startTime);
                        let stopTime = new Date(response.stopTime);
                        let time = (stopTime.getTime() - startTime.getTime()) / 1000.0;

                        if (response.error == undefined && response.errorReason == undefined) {
                            this.setState({failReason: "Timed out after " + time + " seconds", currentStep: VehiclePingToolSteps.ERROR})
                        } else if (response.errorReason != undefined) {
                            this.setState({failReason: response.errorReason, currentStep: VehiclePingToolSteps.ERROR})
                        }
                    } else {
                        this.setState({
                            pingResult: response,
                            currentStep: VehiclePingToolSteps.SUCCESS
                        });

                    }
                    clearInterval(this.interval);
                }
            }).catch((error) => {
                this.setState({failReason: error, currentStep: VehiclePingToolSteps.ERROR})
                clearInterval(this.interval);
            });
    }

    requestFail(result: PingRequestResult) {
        this.setState({failReason: result.message, currentStep: VehiclePingToolSteps.ERROR});
    }

    requestSuccess(result: PingRequestResult) {
        this.setState({correlationId: result.correlationId, currentStep: VehiclePingToolSteps.WAITING_FOR_REPLY});
        this.interval = setInterval(() => this.getPingResult(), this.pingPollPeriod);
    }

    componentDidMount(): void {
        this.setState({formHeight: this.getFormHeight(this.state.currentStep)})
    }

    componentWillUnmount(): void {
        clearInterval(this.interval)
    }

    nextStep() {
        this.onSubmit(this.state.vpi).then(
            (result: PingRequestResult) => {
                if (result.success) {
                    this.requestSuccess(result);
                } else {
                    this.requestFail(result);
                }
            }
        ).catch((result) => this.requestFail(result));
    }

    getFormHeight(tab: VehiclePingToolSteps) {
        switch (tab) {
            case VehiclePingToolSteps.SENDING_REQUEST:
                return 320;
            case VehiclePingToolSteps.ERROR:
                return 330;
            case VehiclePingToolSteps.WAITING_FOR_REPLY:
                return 280;
            case VehiclePingToolSteps.FORM:
                if (this.state.formHeight) {
                    return this.state.formHeight;
                } else {
                    return document.getElementsByClassName("form-component")[0] ? document.getElementsByClassName("form-component")[0].clientHeight + 50 : 0;
                }
            case VehiclePingToolSteps.SUCCESS:
                return 280;
        }
    }

    successScreen() {
        if (this.state.pingResult != undefined) {
            let startTime = new Date(this.state.pingResult.startTime);
            let stopTime = new Date(this.state.pingResult.stopTime);
            let time = stopTime.getTime() - startTime.getTime();

            return (<Container style={{textAlign: "center"}}>
                <FontAwesomeIcon
                    className={"driving-truck-success"}
                    icon={faTruck}
                    style={{height: "120px", width: "120px"}}/>
                <h6></h6>
                <h4>Success</h4>
                <h6>{"Reply after " + time + "ms"}</h6>
                <h6>{"Start time: " + startTime.toLocaleTimeString()}</h6>
                <h6>{"Stop time: " + stopTime.toLocaleTimeString()}</h6>
                <Button
                    onClick={(x: any) => window.location.reload()}>
                    <FontAwesomeIcon icon={faHome}/> Home
                </Button>
            </Container>);
        }
    }

    form(): JSX.Element {
        return (
            <Grid key={"form"} centered>
                <Grid.Row
                    style={{paddingTop: 20, position: "relative", height: this.getFormHeight(this.state.currentStep)}}
                    columns={1}>
                    <Transition animation={this.transitionAnimation} unmountOnHide duration={this.animationDelay}
                                visible={this.state.currentStep == VehiclePingToolSteps.FORM}>
                        <Grid.Column style={{position: "absolute", width: "100%"}}
                                     className={"form-component"}>
                            <Grid.Column>
                                <STCTextField key={"vpi"} placeholderText={"VPI"} value={this.state.vpi}
                                              onChange={(e, name, val) => this.setState({vpi: val, allowNext: true})}/>
                            </Grid.Column>
                        </Grid.Column>
                    </Transition>
                    <Transition animation={this.transitionAnimation} unmountOnHide
                                duration={this.animationDelay}
                                visible={this.state.currentStep == VehiclePingToolSteps.SENDING_REQUEST}>
                        <Grid.Column style={{position: "absolute", width: "100%"}}
                                     className={"form-component"}>
                            {createLoadingScreenBuildingCogs("Pinging...", "Sending request to Vehicle Ping Service...")}
                        </Grid.Column>
                    </Transition>
                    <Transition animation={this.transitionAnimation} unmountOnHide duration={this.animationDelay}
                                visible={this.state.currentStep == VehiclePingToolSteps.WAITING_FOR_REPLY}>
                        <Grid.Column style={{position: "absolute", width: "100%"}}
                                     className={"form-component"}>
                            {createLoadingScreenDrivingTruck("Waiting for reply from vehicle")}
                        </Grid.Column>
                    </Transition>
                    <Transition animation={this.transitionAnimation} unmountOnHide duration={this.animationDelay}
                                visible={this.state.currentStep == VehiclePingToolSteps.SUCCESS}>
                        <Grid.Column style={{position: "absolute", width: "100%"}}
                                     className={"form-component"}>
                            {this.successScreen()}
                        </Grid.Column>
                    </Transition>
                    <Transition animation={this.transitionAnimation} unmountOnHide
                                duration={this.animationDelay}
                                visible={this.state.currentStep == VehiclePingToolSteps.ERROR}>
                        <Grid.Column style={{position: "absolute", width: "100%"}} className={"form-component"}>
                            {createErrorScreen(
                                "Your vehicle ping request failed",
                                undefined,
                                this.state.failReason
                            )}
                        </Grid.Column>
                    </Transition>
                </Grid.Row>
            </Grid>
        )
    }

    buttonsBar() {
        let allowBack: VehiclePingToolSteps[] = [VehiclePingToolSteps.ERROR];
        let allowButtonBar: VehiclePingToolSteps[] = [VehiclePingToolSteps.FORM, VehiclePingToolSteps.ERROR, VehiclePingToolSteps.TRANSITION]
        return (<Container>
                <Grid centered key={"button-bar"}>
                    <Grid.Column width={15}>
                        <Grid.Row hidden={allowButtonBar.indexOf(this.state.currentStep) == -1}>
                            <Grid.Column hidden={allowBack.indexOf(this.state.currentStep) == -1} floated={"left"}>
                                <Button className={"highlight"}
                                        onClick={() => this.previousStep()}
                                        disabled={allowBack.indexOf(this.state.currentStep) == -1}>
                                    <FontAwesomeIcon icon={faAngleLeft} className={"fa-lg"}/> Previous
                                </Button>
                            </Grid.Column>
                            <Grid.Column floated={"right"}>
                                <Button variant={"dark"}
                                        style={{color: CSSService.resolveColour(BaseColours.HIGHLIGHT)}}
                                        onClick={() => this.setState({currentStep: VehiclePingToolSteps.SENDING_REQUEST}, () => this.nextStep())}
                                        disabled={!this.state.allowNext}>
                                    Next <FontAwesomeIcon icon={faAngleRight} className={"fa-lg"}/>
                                </Button>
                            </Grid.Column>
                        </Grid.Row>
                    </Grid.Column>
                </Grid>
            </Container>
        );
    }

    content(): JSX.Element {
        return <Container>
            <Grid>
                <Grid.Column style={{textAlign: "center", marginBottom: 25}} width={16}>
                    <h2>{"Vehicle ping tool"}</h2>
                </Grid.Column>
                <Grid.Column width={16}>
                    {this.form()}
                </Grid.Column>
                <Grid.Row>
                    {this.buttonsBar()}
                </Grid.Row>
            </Grid>
        </Container>
    }
}