package com.volvo.tisp.vehiclepingservice.metrics;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Metrics for handling incoming MO (mobile-originated) messages from vehicles.
 */
@Component
public class MoMessageHandlerMetricReporter {
  static final String MO_MESSAGE_HANDLING = "mo-message-handling";
  static final String TYPE = "TYPE";
  private final Counter invalidServiceVersion;
  private final Counter invalidSrpOption;
  private final Counter processingV1FailureCounter;
  private final Counter processingV1SuccessCounter;
  private final Counter processingV2FailureCounter;
  private final Counter processingV2SuccessCounter;
  private final Counter tokenValidationFailureCounter;
  private final Counter unsupportedVersionCounter;

  public MoMessageHandlerMetricReporter(MeterRegistry meterRegistry) {
    tokenValidationFailureCounter = meterRegistry.counter(MO_MESSAGE_HANDLING, TYPE, "token-validation.failure");
    unsupportedVersionCounter = meterRegistry.counter(MO_MESSAGE_HANDLING, TYPE, "version.unsupported");
    invalidSrpOption = meterRegistry.counter(MO_MESSAGE_HANDLING, TYPE, "invalid-srp-option.invalid");
    invalidServiceVersion = meterRegistry.counter(MO_MESSAGE_HANDLING, TYPE, "invalid-service-version.invalid");
    processingV1SuccessCounter = meterRegistry.counter(MO_MESSAGE_HANDLING, TYPE, "service-v1.success");
    processingV2SuccessCounter = meterRegistry.counter(MO_MESSAGE_HANDLING, TYPE, "service-v2.success");
    processingV1FailureCounter = meterRegistry.counter(MO_MESSAGE_HANDLING, TYPE, "processing-v1.failure");
    processingV2FailureCounter = meterRegistry.counter(MO_MESSAGE_HANDLING, TYPE, "processing-v2.failure");
  }

  public void onInvalidServiceVersion() {
    invalidServiceVersion.increment();
  }

  public void onInvalidSrpOption() {
    invalidSrpOption.increment();
  }

  public void onProcessingV1Failure() {
    processingV1FailureCounter.increment();
  }

  public void onProcessingV1Success() {
    processingV1SuccessCounter.increment();
  }

  public void onProcessingV2Failure() {
    processingV2FailureCounter.increment();
  }

  public void onProcessingV2Success() {
    processingV2SuccessCounter.increment();
  }

  public void onTokenValidationFailure() {
    tokenValidationFailureCounter.increment();
  }

  public void onUnsupportedVersion() {
    unsupportedVersionCounter.increment();
  }
}