package com.volvo.tisp.vehiclepingservice.domain.v1;

import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.jms.MtDto;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;
import com.volvo.tisp.vehiclepingservice.util.TimeoutCalculator;
import com.wirelesscar.telematicunitservice.TelematicUnitService;

@ExtendWith(MockitoExtension.class)
class ReceiveMoStepTest {

  @Mock
  private MtMessagePublisher mtMessagePublisher;
  private ReceiveMoStep receiveMoStep;
  @Mock
  private TelematicUnitService telematicUnitService;
  @Mock
  private TimeoutCalculator timeoutCalculator;

  @BeforeEach
  void setUp() {
    receiveMoStep = new ReceiveMoStep(mtMessagePublisher, telematicUnitService, timeoutCalculator) {
      @Override
      protected byte[] getPayload(MoDtoV1 moDtoV1) {
        return TestUtils.PAYLOAD;
      }
    };
  }

  @Test
  void actionShouldCreateAndPublishMtMessage() {
    MoDtoV1 moDtoV1 = new MoDtoV1();
    String correlationId = "TEST_CORRELATION_ID";
    String receivingChannel = "SMS";
    Channel responseChannel = Channel.SMS;
    String system = "TEST_SYSTEM";
    int timeout = 5000;

    moDtoV1.setVpi(TestUtils.VPI);
    moDtoV1.setCorrelationId(correlationId);
    moDtoV1.setReceivingChannel(receivingChannel);

    Mockito.when(timeoutCalculator.getDefaultTimeout(responseChannel)).thenReturn(timeout);
    Mockito.when(telematicUnitService.getSystem(TestUtils.VPI.toString())).thenReturn(CompletableFuture.completedFuture(system));
    Mockito.when(mtMessagePublisher.publishMtMessage(Mockito.any(MtDto.class))).thenReturn(CompletableFuture.completedFuture(null));

    receiveMoStep.action(moDtoV1);

    Mockito.verify(telematicUnitService).getSystem(TestUtils.VPI.toString());
    Mockito.verify(mtMessagePublisher).publishMtMessage(Mockito.argThat(mtDto -> {
          Assertions.assertEquals(TestUtils.VPI, mtDto.getVpi());
          Assertions.assertEquals(correlationId, mtDto.getCorrelationId());
          Assertions.assertEquals(responseChannel, mtDto.getChannel());
          Assertions.assertEquals(timeout, mtDto.getTimeout());
          Assertions.assertEquals(1, mtDto.getServiceVersion());
          Assertions.assertEquals(system, mtDto.getSystem());
          Assertions.assertArrayEquals(TestUtils.PAYLOAD, mtDto.getPayload());

          return true;
        }
    ));
  }

  @Test
  void actionShouldHandleTelematicServiceFailure() {
    MoDtoV1 moDtoV1 = new MoDtoV1();
    moDtoV1.setVpi(TestUtils.VPI);

    CompletableFuture<String> failedFuture = new CompletableFuture<>();
    failedFuture.completeExceptionally(new RuntimeException("Service failed"));

    Mockito.when(telematicUnitService.getSystem(Mockito.any())).thenReturn(failedFuture);

    receiveMoStep.action(moDtoV1);

    Mockito.verify(telematicUnitService).getSystem(moDtoV1.getVpi().toString());
    Mockito.verifyNoInteractions(mtMessagePublisher);
  }
}
