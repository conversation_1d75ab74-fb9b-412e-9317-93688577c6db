//
// TestServicePdu.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vehiclepingservice.swap.v1.ping;

import java.util.Hashtable;

import com.volvo.tisp.vehiclepingservice.swap.common.ASNChoice;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNException;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNNull;
import com.volvo.tisp.vehiclepingservice.swap.common.ASNValue;

/**
 * TestServicePdu.
 */
public class TestServicePdu extends ASNChoice {
  public static final int E_AUTOPINGRESPONSE = 3;
  public static final int E_AUTOPINGSETUP = 2;
  public static final int E_BEEFYMESSAGE = 4;
  public static final int E_BEEFYMESSAGERESPONSE = 5;
  // Choice element identifier constants
  public static final int E_PING = 0;
  public static final int E_PONG = 1;
  public static final int E_RESERVED06 = 6;
  public static final int E_RESERVED07 = 7;
  public static final int E_RESERVED08 = 8;
  public static final int E_RESERVED09 = 9;
  public static final int E_RESERVED10 = 10;
  public static final int E_RESERVED11 = 11;
  public static final int E_RESERVED12 = 12;
  public static final int E_RESERVED13 = 13;
  public static final int E_RESERVED14 = 14;
  public static final int E_RESERVED15 = 15;
  public static final int E_RESERVED16 = 16;
  public static final int E_RESERVED17 = 17;
  public static final int E_RESERVED18 = 18;
  public static final int E_RESERVED19 = 19;
  public static final int E_RESERVED20 = 20;
  public static final int E_RESERVED21 = 21;
  public static final int E_RESERVED22 = 22;
  public static final int E_RESERVED23 = 23;
  public static final int E_RESERVED24 = 24;
  public static final int E_RESERVED25 = 25;
  public static final int E_RESERVED26 = 26;
  public static final int E_RESERVED27 = 27;
  public static final int E_RESERVED28 = 28;
  public static final int E_RESERVED29 = 29;
  public static final int E_RESERVED30 = 30;
  public static final int E_RESERVED31 = 31;
  protected static final Hashtable<String, Integer> m_nameToValue =
      new Hashtable<String, Integer>();
  protected static final Hashtable<Integer, String> m_valueToName =
      new Hashtable<Integer, String>();
  private static final long serialVersionUID = 1L;

  static {
    m_nameToValue.put("ping", Integer.valueOf(E_PING));
    m_valueToName.put(Integer.valueOf(E_PING), "ping");
    m_nameToValue.put("pong", Integer.valueOf(E_PONG));
    m_valueToName.put(Integer.valueOf(E_PONG), "pong");
    m_nameToValue.put("autoPingSetup", Integer.valueOf(E_AUTOPINGSETUP));
    m_valueToName.put(Integer.valueOf(E_AUTOPINGSETUP), "autoPingSetup");
    m_nameToValue.put("autoPingResponse", Integer.valueOf(E_AUTOPINGRESPONSE));
    m_valueToName.put(Integer.valueOf(E_AUTOPINGRESPONSE), "autoPingResponse");
    m_nameToValue.put("beefyMessage", Integer.valueOf(E_BEEFYMESSAGE));
    m_valueToName.put(Integer.valueOf(E_BEEFYMESSAGE), "beefyMessage");
    m_nameToValue.put("beefyMessageResponse", Integer.valueOf(E_BEEFYMESSAGERESPONSE));
    m_valueToName.put(Integer.valueOf(E_BEEFYMESSAGERESPONSE), "beefyMessageResponse");
    m_nameToValue.put("reserved06", Integer.valueOf(E_RESERVED06));
    m_valueToName.put(Integer.valueOf(E_RESERVED06), "reserved06");
    m_nameToValue.put("reserved07", Integer.valueOf(E_RESERVED07));
    m_valueToName.put(Integer.valueOf(E_RESERVED07), "reserved07");
    m_nameToValue.put("reserved08", Integer.valueOf(E_RESERVED08));
    m_valueToName.put(Integer.valueOf(E_RESERVED08), "reserved08");
    m_nameToValue.put("reserved09", Integer.valueOf(E_RESERVED09));
    m_valueToName.put(Integer.valueOf(E_RESERVED09), "reserved09");
    m_nameToValue.put("reserved10", Integer.valueOf(E_RESERVED10));
    m_valueToName.put(Integer.valueOf(E_RESERVED10), "reserved10");
    m_nameToValue.put("reserved11", Integer.valueOf(E_RESERVED11));
    m_valueToName.put(Integer.valueOf(E_RESERVED11), "reserved11");
    m_nameToValue.put("reserved12", Integer.valueOf(E_RESERVED12));
    m_valueToName.put(Integer.valueOf(E_RESERVED12), "reserved12");
    m_nameToValue.put("reserved13", Integer.valueOf(E_RESERVED13));
    m_valueToName.put(Integer.valueOf(E_RESERVED13), "reserved13");
    m_nameToValue.put("reserved14", Integer.valueOf(E_RESERVED14));
    m_valueToName.put(Integer.valueOf(E_RESERVED14), "reserved14");
    m_nameToValue.put("reserved15", Integer.valueOf(E_RESERVED15));
    m_valueToName.put(Integer.valueOf(E_RESERVED15), "reserved15");
    m_nameToValue.put("reserved16", Integer.valueOf(E_RESERVED16));
    m_valueToName.put(Integer.valueOf(E_RESERVED16), "reserved16");
    m_nameToValue.put("reserved17", Integer.valueOf(E_RESERVED17));
    m_valueToName.put(Integer.valueOf(E_RESERVED17), "reserved17");
    m_nameToValue.put("reserved18", Integer.valueOf(E_RESERVED18));
    m_valueToName.put(Integer.valueOf(E_RESERVED18), "reserved18");
    m_nameToValue.put("reserved19", Integer.valueOf(E_RESERVED19));
    m_valueToName.put(Integer.valueOf(E_RESERVED19), "reserved19");
    m_nameToValue.put("reserved20", Integer.valueOf(E_RESERVED20));
    m_valueToName.put(Integer.valueOf(E_RESERVED20), "reserved20");
    m_nameToValue.put("reserved21", Integer.valueOf(E_RESERVED21));
    m_valueToName.put(Integer.valueOf(E_RESERVED21), "reserved21");
    m_nameToValue.put("reserved22", Integer.valueOf(E_RESERVED22));
    m_valueToName.put(Integer.valueOf(E_RESERVED22), "reserved22");
    m_nameToValue.put("reserved23", Integer.valueOf(E_RESERVED23));
    m_valueToName.put(Integer.valueOf(E_RESERVED23), "reserved23");
    m_nameToValue.put("reserved24", Integer.valueOf(E_RESERVED24));
    m_valueToName.put(Integer.valueOf(E_RESERVED24), "reserved24");
    m_nameToValue.put("reserved25", Integer.valueOf(E_RESERVED25));
    m_valueToName.put(Integer.valueOf(E_RESERVED25), "reserved25");
    m_nameToValue.put("reserved26", Integer.valueOf(E_RESERVED26));
    m_valueToName.put(Integer.valueOf(E_RESERVED26), "reserved26");
    m_nameToValue.put("reserved27", Integer.valueOf(E_RESERVED27));
    m_valueToName.put(Integer.valueOf(E_RESERVED27), "reserved27");
    m_nameToValue.put("reserved28", Integer.valueOf(E_RESERVED28));
    m_valueToName.put(Integer.valueOf(E_RESERVED28), "reserved28");
    m_nameToValue.put("reserved29", Integer.valueOf(E_RESERVED29));
    m_valueToName.put(Integer.valueOf(E_RESERVED29), "reserved29");
    m_nameToValue.put("reserved30", Integer.valueOf(E_RESERVED30));
    m_valueToName.put(Integer.valueOf(E_RESERVED30), "reserved30");
    m_nameToValue.put("reserved31", Integer.valueOf(E_RESERVED31));
    m_valueToName.put(Integer.valueOf(E_RESERVED31), "reserved31");
  }

  public TestServicePdu() {
    setAsnObjectName("TestServicePdu");
    setFixedSizeConstraint(32); // Might be superseeded by a user defined "FixedConstraint"
  }

  public ASNValue createObject() throws ASNException {
    ASNValue oTmp = null;
    switch (m_choiceId) {
      case E_PING:
        oTmp = new Ping();
        oTmp.setAsnObjectName("ping");
        break;
      case E_PONG:
        oTmp = new Pong();
        oTmp.setAsnObjectName("pong");
        break;
      case E_AUTOPINGSETUP:
        oTmp = new AutoPingSetup();
        oTmp.setAsnObjectName("autoPingSetup");
        break;
      case E_AUTOPINGRESPONSE:
        oTmp = new AutoPingResponse();
        oTmp.setAsnObjectName("autoPingResponse");
        break;
      case E_BEEFYMESSAGE:
        oTmp = new BeefyMessage();
        oTmp.setAsnObjectName("beefyMessage");
        break;
      case E_BEEFYMESSAGERESPONSE:
        oTmp = new BeefyMessageResponse();
        oTmp.setAsnObjectName("beefyMessageResponse");
        break;
      case E_RESERVED06:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved06");
        break;
      case E_RESERVED07:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved07");
        break;
      case E_RESERVED08:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved08");
        break;
      case E_RESERVED09:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved09");
        break;
      case E_RESERVED10:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved10");
        break;
      case E_RESERVED11:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved11");
        break;
      case E_RESERVED12:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved12");
        break;
      case E_RESERVED13:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved13");
        break;
      case E_RESERVED14:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved14");
        break;
      case E_RESERVED15:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved15");
        break;
      case E_RESERVED16:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved16");
        break;
      case E_RESERVED17:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved17");
        break;
      case E_RESERVED18:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved18");
        break;
      case E_RESERVED19:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved19");
        break;
      case E_RESERVED20:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved20");
        break;
      case E_RESERVED21:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved21");
        break;
      case E_RESERVED22:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved22");
        break;
      case E_RESERVED23:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved23");
        break;
      case E_RESERVED24:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved24");
        break;
      case E_RESERVED25:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved25");
        break;
      case E_RESERVED26:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved26");
        break;
      case E_RESERVED27:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved27");
        break;
      case E_RESERVED28:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved28");
        break;
      case E_RESERVED29:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved29");
        break;
      case E_RESERVED30:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved30");
        break;
      case E_RESERVED31:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved31");
        break;
      default:
        throw new ASNException(
            "Undefined Choice value ( " + m_choiceId + " ) in type TestServicePdu");
    }
    return oTmp;
  }

  public AutoPingResponse getAutoPingResponse() throws ASNException {
    if (m_choiceId != E_AUTOPINGRESPONSE || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (AutoPingResponse) m_value;
  }

  public AutoPingSetup getAutoPingSetup() throws ASNException {
    if (m_choiceId != E_AUTOPINGSETUP || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (AutoPingSetup) m_value;
  }

  public BeefyMessage getBeefyMessage() throws ASNException {
    if (m_choiceId != E_BEEFYMESSAGE || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (BeefyMessage) m_value;
  }

  public BeefyMessageResponse getBeefyMessageResponse() throws ASNException {
    if (m_choiceId != E_BEEFYMESSAGERESPONSE || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (BeefyMessageResponse) m_value;
  }

  public Ping getPing() throws ASNException {
    if (m_choiceId != E_PING || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (Ping) m_value;
  }

  public Pong getPong() throws ASNException {
    if (m_choiceId != E_PONG || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (Pong) m_value;
  }

  public ASNNull getReserved06() throws ASNException {
    if (m_choiceId != E_RESERVED06 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved07() throws ASNException {
    if (m_choiceId != E_RESERVED07 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved08() throws ASNException {
    if (m_choiceId != E_RESERVED08 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved09() throws ASNException {
    if (m_choiceId != E_RESERVED09 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved10() throws ASNException {
    if (m_choiceId != E_RESERVED10 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved11() throws ASNException {
    if (m_choiceId != E_RESERVED11 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved12() throws ASNException {
    if (m_choiceId != E_RESERVED12 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved13() throws ASNException {
    if (m_choiceId != E_RESERVED13 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved14() throws ASNException {
    if (m_choiceId != E_RESERVED14 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved15() throws ASNException {
    if (m_choiceId != E_RESERVED15 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved16() throws ASNException {
    if (m_choiceId != E_RESERVED16 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved17() throws ASNException {
    if (m_choiceId != E_RESERVED17 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved18() throws ASNException {
    if (m_choiceId != E_RESERVED18 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved19() throws ASNException {
    if (m_choiceId != E_RESERVED19 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved20() throws ASNException {
    if (m_choiceId != E_RESERVED20 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved21() throws ASNException {
    if (m_choiceId != E_RESERVED21 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved22() throws ASNException {
    if (m_choiceId != E_RESERVED22 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved23() throws ASNException {
    if (m_choiceId != E_RESERVED23 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved24() throws ASNException {
    if (m_choiceId != E_RESERVED24 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved25() throws ASNException {
    if (m_choiceId != E_RESERVED25 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved26() throws ASNException {
    if (m_choiceId != E_RESERVED26 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved27() throws ASNException {
    if (m_choiceId != E_RESERVED27 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved28() throws ASNException {
    if (m_choiceId != E_RESERVED28 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved29() throws ASNException {
    if (m_choiceId != E_RESERVED29 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved30() throws ASNException {
    if (m_choiceId != E_RESERVED30 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public ASNNull getReserved31() throws ASNException {
    if (m_choiceId != E_RESERVED31 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public void setAutoPingResponse() throws ASNException {
    if (m_choiceId != E_AUTOPINGRESPONSE || m_value == null) {
      setChoice(E_AUTOPINGRESPONSE);
    }
  }

  public void setAutoPingResponse(AutoPingResponse value) {
    m_choiceId = E_AUTOPINGRESPONSE;
    m_value = value;
  }

  public void setAutoPingSetup() throws ASNException {
    if (m_choiceId != E_AUTOPINGSETUP || m_value == null) {
      setChoice(E_AUTOPINGSETUP);
    }
  }

  public void setAutoPingSetup(AutoPingSetup value) {
    m_choiceId = E_AUTOPINGSETUP;
    m_value = value;
  }

  public void setBeefyMessage() throws ASNException {
    if (m_choiceId != E_BEEFYMESSAGE || m_value == null) {
      setChoice(E_BEEFYMESSAGE);
    }
  }

  public void setBeefyMessage(BeefyMessage value) {
    m_choiceId = E_BEEFYMESSAGE;
    m_value = value;
  }

  public void setBeefyMessageResponse() throws ASNException {
    if (m_choiceId != E_BEEFYMESSAGERESPONSE || m_value == null) {
      setChoice(E_BEEFYMESSAGERESPONSE);
    }
  }

  public void setBeefyMessageResponse(BeefyMessageResponse value) {
    m_choiceId = E_BEEFYMESSAGERESPONSE;
    m_value = value;
  }

  public void setPing() throws ASNException {
    if (m_choiceId != E_PING || m_value == null) {
      setChoice(E_PING);
    }
  }

  public void setPing(Ping value) {
    m_choiceId = E_PING;
    m_value = value;
  }

  public void setPong() throws ASNException {
    if (m_choiceId != E_PONG || m_value == null) {
      setChoice(E_PONG);
    }
  }

  public void setPong(Pong value) {
    m_choiceId = E_PONG;
    m_value = value;
  }

  public void setReserved06() throws ASNException {
    if (m_choiceId != E_RESERVED06 || m_value == null) {
      setChoice(E_RESERVED06);
    }
  }

  public void setReserved06(ASNNull value) {
    m_choiceId = E_RESERVED06;
    m_value = value;
  }

  public void setReserved07() throws ASNException {
    if (m_choiceId != E_RESERVED07 || m_value == null) {
      setChoice(E_RESERVED07);
    }
  }

  public void setReserved07(ASNNull value) {
    m_choiceId = E_RESERVED07;
    m_value = value;
  }

  public void setReserved08() throws ASNException {
    if (m_choiceId != E_RESERVED08 || m_value == null) {
      setChoice(E_RESERVED08);
    }
  }

  public void setReserved08(ASNNull value) {
    m_choiceId = E_RESERVED08;
    m_value = value;
  }

  public void setReserved09() throws ASNException {
    if (m_choiceId != E_RESERVED09 || m_value == null) {
      setChoice(E_RESERVED09);
    }
  }

  public void setReserved09(ASNNull value) {
    m_choiceId = E_RESERVED09;
    m_value = value;
  }

  public void setReserved10() throws ASNException {
    if (m_choiceId != E_RESERVED10 || m_value == null) {
      setChoice(E_RESERVED10);
    }
  }

  public void setReserved10(ASNNull value) {
    m_choiceId = E_RESERVED10;
    m_value = value;
  }

  public void setReserved11() throws ASNException {
    if (m_choiceId != E_RESERVED11 || m_value == null) {
      setChoice(E_RESERVED11);
    }
  }

  public void setReserved11(ASNNull value) {
    m_choiceId = E_RESERVED11;
    m_value = value;
  }

  public void setReserved12() throws ASNException {
    if (m_choiceId != E_RESERVED12 || m_value == null) {
      setChoice(E_RESERVED12);
    }
  }

  public void setReserved12(ASNNull value) {
    m_choiceId = E_RESERVED12;
    m_value = value;
  }

  public void setReserved13() throws ASNException {
    if (m_choiceId != E_RESERVED13 || m_value == null) {
      setChoice(E_RESERVED13);
    }
  }

  public void setReserved13(ASNNull value) {
    m_choiceId = E_RESERVED13;
    m_value = value;
  }

  public void setReserved14() throws ASNException {
    if (m_choiceId != E_RESERVED14 || m_value == null) {
      setChoice(E_RESERVED14);
    }
  }

  public void setReserved14(ASNNull value) {
    m_choiceId = E_RESERVED14;
    m_value = value;
  }

  public void setReserved15() throws ASNException {
    if (m_choiceId != E_RESERVED15 || m_value == null) {
      setChoice(E_RESERVED15);
    }
  }

  public void setReserved15(ASNNull value) {
    m_choiceId = E_RESERVED15;
    m_value = value;
  }

  public void setReserved16() throws ASNException {
    if (m_choiceId != E_RESERVED16 || m_value == null) {
      setChoice(E_RESERVED16);
    }
  }

  public void setReserved16(ASNNull value) {
    m_choiceId = E_RESERVED16;
    m_value = value;
  }

  public void setReserved17() throws ASNException {
    if (m_choiceId != E_RESERVED17 || m_value == null) {
      setChoice(E_RESERVED17);
    }
  }

  public void setReserved17(ASNNull value) {
    m_choiceId = E_RESERVED17;
    m_value = value;
  }

  public void setReserved18() throws ASNException {
    if (m_choiceId != E_RESERVED18 || m_value == null) {
      setChoice(E_RESERVED18);
    }
  }

  public void setReserved18(ASNNull value) {
    m_choiceId = E_RESERVED18;
    m_value = value;
  }

  public void setReserved19() throws ASNException {
    if (m_choiceId != E_RESERVED19 || m_value == null) {
      setChoice(E_RESERVED19);
    }
  }

  public void setReserved19(ASNNull value) {
    m_choiceId = E_RESERVED19;
    m_value = value;
  }

  public void setReserved20() throws ASNException {
    if (m_choiceId != E_RESERVED20 || m_value == null) {
      setChoice(E_RESERVED20);
    }
  }

  public void setReserved20(ASNNull value) {
    m_choiceId = E_RESERVED20;
    m_value = value;
  }

  public void setReserved21() throws ASNException {
    if (m_choiceId != E_RESERVED21 || m_value == null) {
      setChoice(E_RESERVED21);
    }
  }

  public void setReserved21(ASNNull value) {
    m_choiceId = E_RESERVED21;
    m_value = value;
  }

  public void setReserved22() throws ASNException {
    if (m_choiceId != E_RESERVED22 || m_value == null) {
      setChoice(E_RESERVED22);
    }
  }

  public void setReserved22(ASNNull value) {
    m_choiceId = E_RESERVED22;
    m_value = value;
  }

  public void setReserved23() throws ASNException {
    if (m_choiceId != E_RESERVED23 || m_value == null) {
      setChoice(E_RESERVED23);
    }
  }

  public void setReserved23(ASNNull value) {
    m_choiceId = E_RESERVED23;
    m_value = value;
  }

  public void setReserved24() throws ASNException {
    if (m_choiceId != E_RESERVED24 || m_value == null) {
      setChoice(E_RESERVED24);
    }
  }

  public void setReserved24(ASNNull value) {
    m_choiceId = E_RESERVED24;
    m_value = value;
  }

  public void setReserved25() throws ASNException {
    if (m_choiceId != E_RESERVED25 || m_value == null) {
      setChoice(E_RESERVED25);
    }
  }

  public void setReserved25(ASNNull value) {
    m_choiceId = E_RESERVED25;
    m_value = value;
  }

  public void setReserved26() throws ASNException {
    if (m_choiceId != E_RESERVED26 || m_value == null) {
      setChoice(E_RESERVED26);
    }
  }

  public void setReserved26(ASNNull value) {
    m_choiceId = E_RESERVED26;
    m_value = value;
  }

  public void setReserved27() throws ASNException {
    if (m_choiceId != E_RESERVED27 || m_value == null) {
      setChoice(E_RESERVED27);
    }
  }

  public void setReserved27(ASNNull value) {
    m_choiceId = E_RESERVED27;
    m_value = value;
  }

  public void setReserved28() throws ASNException {
    if (m_choiceId != E_RESERVED28 || m_value == null) {
      setChoice(E_RESERVED28);
    }
  }

  public void setReserved28(ASNNull value) {
    m_choiceId = E_RESERVED28;
    m_value = value;
  }

  public void setReserved29() throws ASNException {
    if (m_choiceId != E_RESERVED29 || m_value == null) {
      setChoice(E_RESERVED29);
    }
  }

  public void setReserved29(ASNNull value) {
    m_choiceId = E_RESERVED29;
    m_value = value;
  }

  public void setReserved30() throws ASNException {
    if (m_choiceId != E_RESERVED30 || m_value == null) {
      setChoice(E_RESERVED30);
    }
  }

  public void setReserved30(ASNNull value) {
    m_choiceId = E_RESERVED30;
    m_value = value;
  }

  public void setReserved31() throws ASNException {
    if (m_choiceId != E_RESERVED31 || m_value == null) {
      setChoice(E_RESERVED31);
    }
  }

  public void setReserved31(ASNNull value) {
    m_choiceId = E_RESERVED31;
    m_value = value;
  }

  protected Hashtable<String, Integer> getNameMap() {
    return m_nameToValue;
  }

  protected Hashtable<Integer, String> getValueMap() {
    return m_valueToName;
  }
}

// End of TestServicePdu.java
