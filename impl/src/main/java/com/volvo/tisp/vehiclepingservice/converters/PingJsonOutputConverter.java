package com.volvo.tisp.vehiclepingservice.converters;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.google.gson.Gson;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.NotFoundException;

@Component
public class PingJsonOutputConverter implements Function<PingEntity, String> {
  private final Gson gson;

  public PingJsonOutputConverter(Gson gson) {
    this.gson = gson;
  }

  private static String convertBytesToString(byte[] payload) {
    return payload != null ? new String(payload, StandardCharsets.UTF_8) : null;
  }

  private static String convertToIsoString(Long timeInMillis) {
    return timeInMillis != null ? Instant.ofEpochMilli(timeInMillis).toString() : null;
  }

  @Override
  public String apply(PingEntity pingEntity) {
    if (pingEntity == null) {
      throw new NotFoundException("Ping Data not found");
    }
    return gson.toJson(createOutput(pingEntity));
  }

  private Output createOutput(PingEntity pingEntity) {
    Output output = new Output();
    output.vpi = pingEntity.getVpi();
    output.success = pingEntity.getStatus() == Status.SUCCESS;

    // Handle error and error reason
    if (pingEntity.getError() != null) {
      output.errorReason = pingEntity.getError();
      output.success = Boolean.FALSE;
    } else {
      output.errorReason = getErrorReason(pingEntity.getStatus());
    }

    // Handle timestamps
    output.startTime = convertToIsoString(pingEntity.getStartTimeInMillis());
    output.stopTime = convertToIsoString(pingEntity.getStopTimeInMillis());

    // Handle payloads
    setPayloads(output, pingEntity);

    return output;
  }

  private String getErrorReason(Status status) {
    if (status == Status.SUCCESS) {
      return null;
    }

    return switch (status) {
      case PENDING -> "Ping is PENDING";
      case TIMEOUT -> "Ping Timed out";
      case CANCELLED -> "MtMessage was CANCELLED";
      default -> "Unknown error occurred";
    };
  }

  private void setPayloads(Output output, PingEntity pingEntity) {
    output.payloadSent = convertBytesToString(pingEntity.getPayloadSent());
    output.payloadReceived = convertBytesToString(pingEntity.getPayloadReceived());
  }

  private static class Output {
    String errorReason;
    String payloadReceived;
    String payloadSent;
    String startTime;
    String stopTime;
    Boolean success;
    String vpi;
  }
}
