/*

████████╗██╗  ██╗███████╗███╗   ███╗███████╗███████╗
╚══██╔══╝██║  ██║██╔════╝████╗ ████║██╔════╝██╔════╝
   ██║   ███████║█████╗  ██╔████╔██║█████╗  ███████╗
   ██║   ██╔══██║██╔══╝  ██║╚██╔╝██║██╔══╝  ╚════██║
   ██║   ██║  ██║███████╗██║ ╚═╝ ██║███████╗███████║
   ╚═╝   ╚═╝  ╚═╝╚══════╝╚═╝     ╚═╝╚══════╝╚══════╝

*/

/*******************************
        Theme Selection
*******************************/

/* To override a theme for an individual element
   specify theme name below
*/

/* Global */
@site        : 'activation';
@reset       : 'activation';

/* Elements */
@button      : 'activation';
@container   : 'default';
@divider     : 'default';
@flag        : 'default';
@header      : 'default';
@icon        : 'default';
@image       : 'default';
@input       : 'activation';
@label       : 'default';
@list        : 'default';
@loader      : 'default';
@placeholder : 'default';
@rail        : 'default';
@reveal      : 'default';
@segment     : 'default';
@step        : 'default';

/* Collections */
@breadcrumb  : 'default';
@form        : 'default';
@grid        : 'default';
@menu        : 'activation';
@message     : 'default';
@table       : 'activation';

/* Modules */
@accordion   : 'default';
@checkbox    : 'activation';
@dimmer      : 'default';
@dropdown    : 'activation';
@embed       : 'default';
@modal       : 'activation';
@nag         : 'default';
@popup       : 'default';
@progress    : 'activation';
@rating      : 'default';
@search      : 'default';
@shape       : 'default';
@sidebar     : 'default';
@sticky      : 'default';
@tab         : 'default';
@transition  : 'default';

/* Views */
@ad          : 'default';
@card        : 'default';
@comment     : 'default';
@feed        : 'default';
@item        : 'default';
@statistic   : 'default';

/*******************************
            Folders
*******************************/

@themesFolder : '../../node_modules/support-tool-components/src/semantic-ui/themes';
@siteFolder  : '../../src/semantic-ui/site';

@import (multiple) "~semantic-ui-less/theme.less";
@fontPath : '../../../themes/@{theme}/assets/fonts';


/*******************************
         Import Theme
*******************************/


@import (multiple) "~semantic-ui-less/theme.less";
@fontPath : "../../../themes/@{theme}/assets/fonts";

/* End Config */
