import {FetchService} from "support-tool-components";


export class RequestService {

    public static async ping(vpi: string) {
        let request = FetchService.fetch(
            "vehicleping/vpi/" + vpi,
            "GET",
            {"Accept": "text/plain"});
        return RequestService.extractStringResponseFromRequest(request);
    }

    public static async customPing(vpi: string, cgw: string) {
        let request = FetchService.fetch(
            "vehicleping/custom/vpi/" + vpi +"/communicationgateway/" + cgw,
            "GET",
            {"Accept": "text/plain"});
        return RequestService.extractStringResponseFromRequest(request);
    }

    public static async beefy(vpi: string, base64Data: string) {
        let request = FetchService.fetch(
            "beefy/vpi/" + vpi,
            "POST",
            {"Accept": "text/plain"},
            base64Data);
        return RequestService.extractStringResponseFromRequest(request);
    }

    public static async customBeefy(vpi: string, base64Data: string, cgw: string) {
        let request = FetchService.fetch(
            "beefy/vpi/" + vpi + "/communicationgateway/" + cgw,
            "POST",
            {"Accept": "text/plain"},
            base64Data);
        return RequestService.extractStringResponseFromRequest(request);
    }

    public static async getPingResult(correlationId: string) {
        let request = FetchService.fetch(
            "vehicleping/data/correlationid/" + correlationId,
            "GET",
            {"Accept": "application/json"});

        return FetchService.extractJsonResponseFromRequest(request);
    }

    public static extractStringResponseFromRequest(request: Promise<any>) {
        return request.then(response => {
            return response
        }).then((response: Response) => {
            if(!response.ok){
                return Promise.reject(response.statusText);
            }
            return response.text().then(
                (resulterino) => resulterino
            )
        }).catch((response) => {
            return Promise.reject(response);
        });
    }
}