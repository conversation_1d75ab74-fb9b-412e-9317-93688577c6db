package com.volvo.tisp.vehiclepingservice.jms;

import static com.volvo.tisp.vehiclepingservice.jms.MtStatusJmsHandler.MT_STATUS_IN_QUEUE;

import java.util.Locale;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.context.Vehicle;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.identifier.VehicleIdentifier;
import com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtStatus;
import com.volvo.tisp.vc.mt.message.client.json.v1.Status;
import com.volvo.tisp.vehiclepingservice.domain.PingManager;
import com.volvo.tisp.vehiclepingservice.metrics.MtStatusMetricReporter;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.InternalServerErrorException;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

@JmsController(destination = MT_STATUS_IN_QUEUE)
public class MtStatusJmsHandler {
  public static final String MT_STATUS_IN_QUEUE = "MTSTATUS.IN";
  private static final Logger logger = LoggerFactory.getLogger(MtStatusJmsHandler.class);

  private final PingManager pingManager;
  private final MtStatusMetricReporter mtStatusMetricReporter;

  @Autowired
  public MtStatusJmsHandler(PingManager pingManager, MtStatusMetricReporter mtStatusMetricReporter) {
    this.pingManager = pingManager;
    this.mtStatusMetricReporter = mtStatusMetricReporter;
  }

  @JmsMessageMapping(consumesType = MessageTypesJms.TCE_MTSTATUS_MESSAGE_TYPE, consumesVersion = MessageTypesJms.VERSION_2_0)
  public CompletableFuture<Void> pingStatus(final JmsMessage<MtStatusMessage> message) {
    try (final TispContext.Scope ignored =
        TispContext.current()
            .newScope()
            .vehicle(
                Vehicle.builder()
                    .vpi(VehicleIdentifier.fromString(message.payload().getVehiclePlatformId())))
            .activate()) {
      Status status = convertToNewestStatusType(
              message.payload().getStatus(),
              message.payload().getVehiclePlatformId(),
              message.payload().getCorrelationId());
      return pingManager.processMtStatus(
          message.payload().getCorrelationId(), status)
          .thenApply(v -> {
            switch (status) {
              case DELIVERED -> mtStatusMetricReporter.onDelivered();
              case FAILED   -> mtStatusMetricReporter.onFailed();
              case TIMEOUT  -> mtStatusMetricReporter.onTimeout();
              case CANCELED -> mtStatusMetricReporter.onCanceled();
              case OVERRIDDEN -> mtStatusMetricReporter.onOverridden();
              default -> mtStatusMetricReporter.onFailed();
            }
            return v;
          })
          .exceptionally(throwable -> {
            mtStatusMetricReporter.onFailed();
            throw new RuntimeException(throwable);
          });
    }
  }

  @JmsMessageMapping(consumesType = MessageTypes.MT_STATUS, consumesVersion = MessageTypes.VERSION_1_0)
  public CompletableFuture<Void> pingStatusMoRouter(final JmsMessage<MtStatus> message) {
    try (final TispContext.Scope ignored =
        TispContext.current()
            .newScope()
            .vehicle(
                Vehicle.builder()
                    .vpi(VehicleIdentifier.fromString(message.payload().getVehiclePlatformId())))
            .activate()) {
      Status status = message.payload().getStatus();
      return pingManager.processMtStatus(
          message.payload().getCorrelationId(), status)
          .thenApply(v -> {
            switch (status) {
              case DELIVERED -> mtStatusMetricReporter.onDelivered();
              case FAILED   -> mtStatusMetricReporter.onFailed();
              case TIMEOUT  -> mtStatusMetricReporter.onTimeout();
              case CANCELED -> mtStatusMetricReporter.onCanceled();
              case OVERRIDDEN -> mtStatusMetricReporter.onOverridden();
              default -> mtStatusMetricReporter.onFailed();
            }
            return v;
          })
          .exceptionally(throwable -> {
            mtStatusMetricReporter.onFailed();
            throw new RuntimeException(throwable);
          });
    }
  }

  private Status convertToNewestStatusType(String status, String vpi, String correlationId) {
    return switch (status) {
      case "TIMEOUT" -> Status.TIMEOUT;
      case "DELIVERED" -> Status.DELIVERED;
      case "CANCELLED", "CANCELED" -> Status.CANCELED;
      case "FAILED", "ERROR", "REJECTED", "SERVICE_UNSUPPORTED" -> Status.FAILED;
      case "OVERRIDDEN", "OVERRIDEN" -> Status.OVERRIDDEN;
      default -> {
        String err = String.format(Locale.ROOT, "Unknown MtStatus %s for vpi: %s with correlation id %s received", status, vpi, correlationId);
        logger.error(err);
        throw new InternalServerErrorException(err);
      }
    };
  }
}
