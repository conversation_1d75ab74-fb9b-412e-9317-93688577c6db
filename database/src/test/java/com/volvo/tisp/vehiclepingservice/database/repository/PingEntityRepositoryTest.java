package com.volvo.tisp.vehiclepingservice.database.repository;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;

class PingEntityRepositoryTest extends RepositoryTestBase {

  @Autowired
  private PingEntityRepository repository;

  @BeforeEach
  void cleanup() {
    repository.deleteAll().block();
  }

  @Test
  void findByCorrelationIdWhenEntityDoesNotExistShouldReturnNull() {
    PingEntity found = repository.findByCorrelationId("non-existent-id").block();

    Assertions.assertNull(found);
  }

  @Test
  void findByCorrelationIdWhenEntityExistsShouldReturnEntity() {
    PingEntity entity = new PingEntity();
    entity.setCorrelationId("test-correlation-id");
    repository.save(entity).block();

    PingEntity found = repository.findByCorrelationId("test-correlation-id").block();

    Assertions.assertNotNull(found);
    Assertions.assertEquals("test-correlation-id", found.getCorrelationId());
  }

  @Test
  void findByMessageIdWhenEntityDoesNotExistShouldReturnNull() {
    PingEntity found = repository.findByMessageId(123L).block();

    Assertions.assertNull(found);
  }

  @Test
  void findByMessageIdWhenEntityExistsShouldReturnEntity() {
    // Arrange
    long messageId = 111L;
    PingEntity entity = new PingEntity();
    entity.setMessageId(111L);
    repository.save(entity).block();

    // Act
    PingEntity found = repository.findByMessageId(messageId).block();

    // Assert
    Assertions.assertNotNull(found);
    Assertions.assertEquals(messageId, found.getMessageId());
  }

}
