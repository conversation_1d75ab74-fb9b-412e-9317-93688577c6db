{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "enable": true, "hide": false, "iconColor": "blue", "name": "activated", "target": {"fromAnnotations": true, "limit": 100, "name": "activated", "query": "SELECT value, type, version, host_id, short_name FROM \"${days}\".\"app-event\" WHERE $timeFilter AND site = '${site}' AND environment = '${env}' AND solution= '${solution}' AND short_name='${short_name}' and type = 'ACTIVATED' and value > 0", "queryType": "tags", "rawQuery": true, "refId": "", "tags": [], "tagsColumn": " type,version,host_id,short_name", "textColumn": "ACTIVATED", "textEditor": true, "timeEndColumn": "", "titleColumn": "", "type": "dashboard"}}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "enable": true, "hide": false, "iconColor": "orange", "name": "deactivated", "target": {"fromAnnotations": true, "limit": 100, "name": "deactivated", "query": "SELECT value, type, version, host_id, short_name FROM \"${days}\".\"app-event\" WHERE $timeFilter AND site = '${site}' AND environment = '${env}' AND solution= '${solution}' AND short_name='${short_name}' and type = 'DEACTIVATED' and value > 0", "queryType": "tags", "rawQuery": true, "refId": "", "tags": [], "tagsColumn": " type,version,host_id,short_name", "textColumn": "DEACTIVATED", "textEditor": true, "timeEndColumn": "", "titleColumn": "", "type": "dashboard"}}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "enable": true, "hide": false, "iconColor": "green", "name": "started", "target": {"fromAnnotations": true, "limit": 100, "name": "started", "query": "SELECT value, type, version, host_id, short_name FROM \"${days}\".\"app-event\" WHERE $timeFilter AND site = '${site}' AND environment = '${env}' AND solution= '${solution}' AND short_name='${short_name}' and type = 'STARTED' and value > 0", "queryType": "tags", "rawQuery": true, "refId": "", "tags": [], "tagsColumn": " type,version,host_id,short_name", "textColumn": "STARTED", "textEditor": true, "timeEndColumn": "", "titleColumn": "", "type": "dashboard"}}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "enable": true, "hide": false, "iconColor": "red", "name": "stopped", "target": {"fromAnnotations": true, "limit": 100, "matchAny": false, "query": "SELECT value, type, version, host_id, short_name FROM \"${days}\".\"app-event\" WHERE $timeFilter AND site = '${site}' AND environment = '${env}' AND solution= '${solution}' AND short_name='${short_name}' and type = 'STOPPED' and value > 0", "rawQuery": true, "tags": [], "tagsColumn": "type,short_name,host_id,version", "textColumn": "STOPPED", "textEditor": true, "type": "dashboard"}}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4471, "links": [], "panels": [{"datasource": {"type": "influxdb", "uid": "000000144"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 25, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "influxdb", "uid": "000000144"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["address"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "messaging.queue", "orderByTime": "ASC", "policy": "7days", "query": "SELECT sum(\"QueueSize\") FROM \"7days\".\"messaging.queue\" WHERE (\"address\"::tag =~ /^SHARED\\.EU-WEST-1\\.PROD\\.(?i:vps)\\./) AND $timeFilter GROUP BY time(1m), \"address\" fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["QueueSize"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "address::tag", "operator": "=~", "value": "/^SHARED\\.EU-WEST-1\\.QA\\.(?i:$vacomponent)\\./"}]}, {"datasource": {"type": "influxdb", "uid": "000000144"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["address"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "messaging.queue", "orderByTime": "ASC", "policy": "7days", "query": "SELECT sum(\"QueueSize\") FROM \"7days\".\"messaging.queue\" WHERE (\"address\"::tag =~ /^SHARED\\.US-EAST-1\\.PROD\\.(?i:vps)\\./) AND $timeFilter GROUP BY time(1m), \"address\" fill(null)", "rawQuery": true, "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["QueueSize"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "address::tag", "operator": "=~", "value": "/^SHARED\\.US-EAST-1\\.QA\\.(?i:$vacomponent)\\./"}]}, {"datasource": {"type": "influxdb", "uid": "000000144"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["address"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "messaging.queue", "orderByTime": "ASC", "policy": "7days", "query": "SELECT sum(\"QueueSize\") FROM \"7days\".\"messaging.queue\" WHERE (\"address\"::tag =~ /^SHARED\\.CN-NW-1\\.PROD\\.(?i:vps)\\./) AND $timeFilter GROUP BY time(1m), \"address\" fill(null)", "rawQuery": true, "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["QueueSize"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "address::tag", "operator": "=~", "value": "/^SHARED\\.CN-NW-1\\.QA\\.(?i:$vacomponent)\\./"}]}], "title": "JMS Queue Size", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 10, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "none"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["http_server_requests.mean {short_name: vps, site: us-east-1, status: 200}"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 0}, "id": 3, "options": {"legend": {"calcs": ["last"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["status::tag"], "type": "tag"}, {"params": ["short_name"], "type": "tag"}, {"params": ["site::tag"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "http_server_requests", "orderByTime": "ASC", "policy": "60days", "query": "SELECT mean(\"count\") FROM \"60days\".\"http_server_requests\" WHERE (\"uri\"::tag = '/actuator/health' AND \"environment\"::tag = 'qa' AND \"owner\"::tag = 'VehicleCommunication') AND $timeFilter GROUP BY time(1m), \"status\"::tag, \"short_name\", \"site\"::tag fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["count"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"condition": "AND", "key": "uri::tag", "operator": "=", "value": "/actuator/health"}, {"condition": "AND", "key": "environment::tag", "operator": "=", "value": "qa"}, {"condition": "AND", "key": "owner::tag", "operator": "=", "value": "VehicleCommunication"}]}], "title": "Health_Check", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000144"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 8, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "influxdb", "uid": "000000144"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["address"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "messaging.queue", "orderByTime": "ASC", "policy": "7days", "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["QueueSize"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "address::tag", "operator": "=~", "value": "/^SHARED\\.EU-WEST-1\\.QA\\.(?i:$vacomponent)\\./"}]}, {"datasource": {"type": "influxdb", "uid": "000000144"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["address"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "messaging.queue", "orderByTime": "ASC", "policy": "7days", "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["QueueSize"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "address::tag", "operator": "=~", "value": "/^SHARED\\.US-EAST-1\\.QA\\.(?i:$vacomponent)\\./"}]}, {"datasource": {"type": "influxdb", "uid": "000000144"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["address"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "messaging.queue", "orderByTime": "ASC", "policy": "7days", "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["QueueSize"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "address::tag", "operator": "=~", "value": "/^SHARED\\.CN-NW-1\\.QA\\.(?i:$vacomponent)\\./"}]}], "title": "JMS Queue Size", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000144"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 26, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "influxdb", "uid": "000000144"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["address"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "messaging.queue", "orderByTime": "ASC", "policy": "7days", "query": "SELECT sum(\"QueueSize\") FROM \"7days\".\"messaging.queue\" WHERE (\"address\"::tag =~ /^DLQ.SHARED\\.EU-WEST-1\\.PROD\\.(?i:vps)\\./) AND $timeFilter GROUP BY time(1m), \"address\" fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["QueueSize"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "address::tag", "operator": "=~", "value": "/^SHARED\\.EU-WEST-1\\.QA\\.(?i:$vacomponent)\\./"}]}, {"datasource": {"type": "influxdb", "uid": "000000144"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["address"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "messaging.queue", "orderByTime": "ASC", "policy": "7days", "query": "SELECT sum(\"QueueSize\") FROM \"7days\".\"messaging.queue\" WHERE (\"address\"::tag =~ /^DLQ.SHARED\\.US-EAST-1\\.PROD\\.(?i:vps)\\./) AND $timeFilter GROUP BY time(1m), \"address\" fill(null)", "rawQuery": true, "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["QueueSize"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "address::tag", "operator": "=~", "value": "/^SHARED\\.US-EAST-1\\.QA\\.(?i:$vacomponent)\\./"}]}, {"datasource": {"type": "influxdb", "uid": "000000144"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["address"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "messaging.queue", "orderByTime": "ASC", "policy": "7days", "query": "SELECT sum(\"QueueSize\") FROM \"7days\".\"messaging.queue\" WHERE (\"address\"::tag =~ /^DLQ.SHARED\\.CN-NW-1\\.PROD\\.(?i:vps)\\./) AND $timeFilter GROUP BY time(1m), \"address\" fill(null)", "rawQuery": true, "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["QueueSize"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "address::tag", "operator": "=~", "value": "/^SHARED\\.CN-NW-1\\.QA\\.(?i:$vacomponent)\\./"}]}], "title": "JMS DLQ QueueSize", "type": "timeseries"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 2, "options": {"alertInstanceLabelFilter": "", "alertName": "", "dashboardAlerts": true, "datasource": "-- <PERSON><PERSON> --", "folder": {"title": "VehicleCommunication", "uid": "aca66861-9a77-4544-86cb-d2c8f6712ee6"}, "groupBy": [], "groupMode": "default", "maxItems": 20, "showInactiveAlerts": false, "sortOrder": 1, "stateFilter": {"error": true, "firing": true, "noData": false, "normal": false, "pending": true}, "viewMode": "list"}, "pluginVersion": "11.6.3", "title": "<PERSON><PERSON><PERSON>", "type": "alertlist"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 27, "panels": [], "title": "Application Metrics", "type": "row"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 25}, "id": 28, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["TYPE"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "mt-message-publisher", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT sum(\"count\") FROM \"[[days]]\".\"mt-message-publisher\" WHERE (\"environment\"::tag =~ /^$env$/ AND \"site\"::tag =~ /^$site$/ AND \"short_name\"::tag = 'vps') AND $timeFilter GROUP BY time(1m), \"TYPE\"::tag fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["count"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "environment::tag", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "site::tag", "operator": "=~", "value": "/^$site$/"}, {"condition": "AND", "key": "short_name::tag", "operator": "=", "value": "vps"}]}], "title": "MT Message Publisher", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "id": 29, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["PING"], "type": "tag"}, {"params": ["PONG"], "type": "tag"}, {"params": ["VERSION"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "ping-manager", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT sum(\"count\") FROM \"[[days]]\".\"ping-manager\" WHERE (\"environment\"::tag =~ /^$env$/ AND \"site\"::tag =~ /^$site$/ AND \"short_name\"::tag = 'vps') AND $timeFilter GROUP BY time(1m), \"PING\"::tag, \"PONG\"::tag, \"VERSION\"::tag, \"beefy\"::tag, \"beefyresponse\"::tag fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["count"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "environment::tag", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "site::tag", "operator": "=~", "value": "/^$site$/"}, {"condition": "AND", "key": "short_name::tag", "operator": "=", "value": "vps"}]}], "title": "Ping Manager - Vehicle Messages", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 34}, "id": 10, "panels": [], "title": "JWM Memory", "type": "row"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 35}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "[[tag_host_id]]_max", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "jvm_memory_max", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT mean(\"value\") FROM \"[[days]]\".\"jvm_memory_max\" WHERE (\"environment\" =~ /^$env$/ AND \"site\" =~ /^$site$/ AND \"short_name\" = 'vps') AND $timeFilter GROUP BY time($__interval), \"host_id\" fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}]}], "title": "Max", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 35}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "[[tag_host_id]]_used", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "jvm_memory_used", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT mean(\"value\") FROM \"[[days]]\".\"jvm_memory_used\" WHERE (\"environment\" =~ /^$env$/ AND \"site\" =~ /^$site$/ AND \"short_name\" = 'vps') AND $timeFilter GROUP BY time($__interval), \"host_id\" fill(null)", "rawQuery": true, "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}]}], "title": "Used", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 43}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "[[tag_host_id]]_commited", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "jvm_memory_committed", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT mean(\"value\") FROM \"[[days]]\".\"jvm_memory_committed\" WHERE (\"environment\" =~ /^$env$/ AND \"site\" =~ /^$site$/ AND \"short_name\" = 'vps') AND $timeFilter GROUP BY time($__interval), \"host_id\" fill(null)", "rawQuery": true, "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}]}], "title": "Commited", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 51}, "id": 12, "panels": [], "title": "JVM Threads", "type": "row"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 52}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "[[tag_host_id]]_thread_live", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "jvm_threads_live", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT mean(\"value\") FROM \"[[days]]\".\"jvm_threads_live\" WHERE (\"environment\" =~ /^$env$/ AND \"short_name\" = 'vps' AND \"site\" =~ /^$site$/) AND $timeFilter GROUP BY time($__interval), \"host_id\" fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}]}], "title": "Live", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 52}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "[[tag_host_id]]_thread_daemon", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "jvm_threads_daemon", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT mean(\"value\") FROM \"[[days]]\".\"jvm_threads_daemon\" WHERE (\"environment\" =~ /^$env$/ AND \"short_name\" = 'vps' AND \"site\" =~ /^$site$/) AND $timeFilter GROUP BY time($__interval), \"host_id\" fill(null)", "rawQuery": true, "refId": "B", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}]}], "title": "Daemon", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 60}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "[[tag_host_id]]_thread_peak", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "jvm_threads_peak", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT mean(\"value\") FROM \"[[days]]\".\"jvm_threads_peak\" WHERE (\"environment\" =~ /^$env$/ AND \"short_name\" = 'vps' AND \"site\" =~ /^$site$/) AND $timeFilter GROUP BY time($__interval), \"host_id\" fill(null)", "rawQuery": true, "refId": "C", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}]}], "title": "Peak", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 60}, "id": 19, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "[[tag_host_id]]_thread_state_[[tag_state]]", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host_id"], "type": "tag"}, {"params": ["state"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "hide": false, "measurement": "jvm_threads_states", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT mean(\"value\") FROM \"[[days]]\".\"jvm_threads_states\" WHERE (\"environment\" =~ /^$env$/ AND \"short_name\" = 'vps' AND \"site\" =~ /^$site$/) AND $timeFilter GROUP BY time($__interval), \"host_id\", \"state\" fill(null)", "rawQuery": true, "refId": "D", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}]}], "title": "State", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 68}, "id": 13, "panels": [], "title": "JVM GC", "type": "row"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "description": "Time spent in GC pause", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "Pauses / Minute", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 1, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 69}, "id": 20, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "$tag_cause ($tag_action)  [[tag_host_id]]", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["action"], "type": "tag"}, {"params": ["cause"], "type": "tag"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "jvm_gc_pause", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT sum(\"count\") FROM \"[[days]]\".\"jvm_gc_pause\" WHERE (\"environment\" =~ /^$env$/ AND \"solution\" = 'shared' AND \"site\" =~ /^$site$/ AND \"short_name\" = 'vps') AND $timeFilter GROUP BY time(1m), \"action\", \"cause\", \"host_id\" fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["count"], "type": "field"}, {"params": [], "type": "sum"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "solution", "operator": "=", "value": "shared"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}], "target": ""}], "title": "GC Pause Count", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "description": "Time spent in GC pause", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 69}, "id": 21, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "$tag_cause ($tag_action)  [[tag_host_id]]", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["action"], "type": "tag"}, {"params": ["cause"], "type": "tag"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "jvm_gc_pause", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT percentile(\"mean\", 95) FROM \"[[days]]\".\"jvm_gc_pause\" WHERE (\"environment\" =~ /^$env$/ AND \"solution\" = 'shared' AND \"site\" =~ /^$site$/ AND \"short_name\" = 'vps') AND $timeFilter GROUP BY time($__interval), \"action\", \"cause\", \"host_id\" fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["mean"], "type": "field"}, {"params": [95], "type": "percentile"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "solution", "operator": "=", "value": "shared"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}], "target": ""}], "title": "GC Pause Duration", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 77}, "id": 11, "panels": [], "title": "Misc", "type": "row"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 78}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "[[tag_host_id]]_cpu_usage", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "system_cpu_usage", "orderByTime": "ASC", "policy": "$days", "query": "SELECT last(\"value\") FROM \"$days\".\"system_cpu_usage\" WHERE (\"environment\" =~ /^$env$/ AND \"site\" =~ /^$site$/ AND \"short_name\" =~ /^$short_name$/) AND $timeFilter GROUP BY time(1m), \"host_id\" fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "last"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}, {"condition": "AND", "key": "short_name", "operator": "=~", "value": "/^$short_name$/"}]}], "title": "CPU Usage", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 77}, "id": 22, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "[[tag_host_id]]_system_load", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["1m"], "type": "time"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "system_load_average_1m", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT mean(\"value\") FROM \"[[days]]\".\"system_load_average_1m\" WHERE (\"environment\" =~ /^$env$/ AND \"site\" =~ /^$site$/ AND \"short_name\" = 'vps') AND $timeFilter GROUP BY time(1m), \"host_id\" fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}]}], "title": "System Load Average 1m", "type": "timeseries"}, {"datasource": {"type": "influxdb", "uid": "000000141"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 85}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.3", "targets": [{"alias": "[[tag_host_id]]_open_files", "datasource": {"type": "influxdb", "uid": "000000141"}, "groupBy": [{"params": ["$__interval"], "type": "time"}, {"params": ["host_id"], "type": "tag"}, {"params": ["null"], "type": "fill"}], "measurement": "process_files_open", "orderByTime": "ASC", "policy": "[[days]]", "query": "SELECT mean(\"value\") FROM \"[[days]]\".\"process_files_open\" WHERE (\"environment\" =~ /^$env$/ AND \"site\" =~ /^$site$/ AND \"short_name\" = 'vps') AND $timeFilter GROUP BY time($__interval), \"host_id\" fill(null)", "rawQuery": true, "refId": "A", "resultFormat": "time_series", "select": [[{"params": ["value"], "type": "field"}, {"params": [], "type": "mean"}]], "tags": [{"key": "environment", "operator": "=~", "value": "/^$env$/"}, {"condition": "AND", "key": "site", "operator": "=~", "value": "/^$site$/"}, {"condition": "AND", "key": "short_name", "operator": "=", "value": "/^$short_name$/"}]}], "title": "Process Files Open", "type": "timeseries"}], "preload": false, "schemaVersion": 41, "tags": [], "templating": {"list": [{"current": {"text": "60days", "value": "60days"}, "label": "days", "name": "days", "options": [{"selected": false, "text": "30days", "value": "30days"}, {"selected": true, "text": "60days", "value": "60days"}, {"selected": false, "text": "365days", "value": "365days"}], "query": "30days, 60days, 365days", "type": "custom"}, {"current": {"text": "eu-west-1", "value": "eu-west-1"}, "datasource": {"type": "influxdb", "uid": "000000141"}, "definition": "SELECT \"site\" FROM ( SELECT last(value) FROM \"${days}\".\"jvm_buffer_count\" GROUP BY \"site\", \"short_name\") WHERE (\"short_name\"='vps')", "name": "site", "options": [], "query": {"query": "SELECT \"site\" FROM ( SELECT last(value) FROM \"${days}\".\"jvm_buffer_count\" GROUP BY \"site\", \"short_name\") WHERE (\"short_name\"='vps')", "refId": "InfluxVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "test", "value": "test"}, "datasource": {"type": "influxdb", "uid": "000000141"}, "definition": "SELECT \"environment\" FROM ( SELECT last(value) FROM \"${days}\".\"jvm_buffer_count\" GROUP BY \"environment\", \"short_name\") WHERE (\"short_name\"='vps')", "name": "env", "options": [], "query": {"query": "SELECT \"environment\" FROM ( SELECT last(value) FROM \"${days}\".\"jvm_buffer_count\" GROUP BY \"environment\", \"short_name\") WHERE (\"short_name\"='vps')", "refId": "InfluxVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "vps", "value": "vps"}, "name": "short_name", "options": [{"selected": true, "text": "vps", "value": "vps"}], "query": "vps", "type": "custom"}, {"current": {"text": "TRUE", "value": "TRUE"}, "name": "events", "options": [{"selected": true, "text": "ON", "value": "TRUE"}, {"selected": false, "text": "OFF", "value": "FALSE"}], "query": "ON : TRUE, OFF : FALSE", "type": "custom"}, {"current": {"text": "shared", "value": "shared"}, "datasource": {"type": "influxdb", "uid": "000000141"}, "definition": "SELECT \"solution\" FROM ( SELECT last(value) FROM \"${days}\".\"jvm_buffer_count\" GROUP BY \"solution\", \"short_name\") WHERE (\"short_name\"='$short_name')", "name": "solution", "options": [], "query": {"query": "SELECT \"solution\" FROM ( SELECT last(value) FROM \"${days}\".\"jvm_buffer_count\" GROUP BY \"solution\", \"short_name\") WHERE (\"short_name\"='$short_name')", "refId": "InfluxVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-90d", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "browser", "title": "vehicle-ping-service-server", "uid": "ceikpjdmsxs00e", "version": 24}