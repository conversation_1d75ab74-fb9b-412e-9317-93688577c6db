package com.volvo.tisp.vehiclepingservice.rest;

import java.util.concurrent.CompletableFuture;

import org.springframework.context.annotation.Conditional;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.vehiclepingservice.conf.TestEnvironmentCondition;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;

@Conditional(value = TestEnvironmentCondition.class)
@RestController()
@Authentication(required = false)
@RequestMapping(TestRestHandler.REQUEST_PATH)
public class TestRestHandler {
  public static final String REQUEST_PATH = "test";
  private final MtMessagePublisher mtMessagePublisher;
  private final PingEntityService pingEntityService;
  private final ServiceAccessTokenService serviceAccessTokenService;

  public TestRestHandler(PingEntityService pingEntityService, MtMessagePublisher mtMessagePublisher, ServiceAccessTokenService serviceAccessTokenService) {
    this.pingEntityService = pingEntityService;
    this.mtMessagePublisher = mtMessagePublisher;
    this.serviceAccessTokenService = serviceAccessTokenService;
  }

  @RequestMapping(method = RequestMethod.GET, path = "clearDB")
  public CompletableFuture<Void> clearDb() {
    return pingEntityService.removeAll();
  }

  @GetMapping(path = "count", produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletableFuture<Long> count() {
    return pingEntityService.count();
  }

  @GetMapping(path = "get/{corrid}", produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletableFuture<PingEntity> getFromDb(@PathVariable("corrid") String corrId) {
    return pingEntityService.findByCorrelationId(corrId);
  }

  @GetMapping(path = "mtdispatcher", produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletableFuture<Boolean> getMtDispatcherFeature() {
    return CompletableFuture.completedFuture(mtMessagePublisher.getMtDispatcherFeature());
  }

  @GetMapping(path = "servicetokenvalidation", produces = MediaType.APPLICATION_JSON_VALUE)
  public CompletableFuture<Boolean> getOBSServiceAccessTokenValidationFeature() {
    return CompletableFuture.completedFuture(
        serviceAccessTokenService.isServiceAccessTokenValidationEnabled());
  }

  @PutMapping(path = "mtdispatcher/{value}")
  public CompletableFuture<Void> setMtDispatcher(@PathVariable("value") Boolean value) {
    mtMessagePublisher.setMtDispatcherFeature(value);
    return CompletableFuture.completedFuture(null);
  }

  @PutMapping(path = "servicetokenvalidation/{enabled}")
  public CompletableFuture<Void> setOBSServiceAccessTokenValidationFeature(@PathVariable("enabled") Boolean value) {
    serviceAccessTokenService.setServiceAccessTokenValidationEnabled(value);
    return CompletableFuture.completedFuture(null);
  }
}
