package com.volvo.tisp.vehiclepingservice.jms;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;

public class MtDto {
  private Channel channel;
  private String correlationId;
  private byte[] payload;
  private String serviceAccessToken;
  private int serviceVersion;
  private String system;
  private int timeout;
  private Vpi vpi;

  public Channel getChannel() {
    return channel;
  }

  public String getCorrelationId() {
    return correlationId;
  }

  public byte[] getPayload() {
    return payload;
  }

  public String getServiceAccessToken() {
    return serviceAccessToken;
  }

  public int getServiceVersion() {
    return serviceVersion;
  }

  public String getSystem() {
    return system;
  }

  public int getTimeout() {
    return timeout;
  }

  public Vpi getVpi() {
    return vpi;
  }

  public void setChannel(Channel channel) {
    this.channel = channel;
  }

  public void setCorrelationId(String correlationId) {
    this.correlationId = correlationId;
  }

  public void setPayload(byte[] payload) {
    this.payload = payload;
  }

  // MtDispatcher Unique
  public MtDto setServiceAccessToken(String serviceAccessToken) {
    this.serviceAccessToken = serviceAccessToken;
    return this;
  }

  public void setServiceVersion(int serviceVersion) {
    this.serviceVersion = serviceVersion;
  }

  public void setSystem(String system) {
    this.system = system;
  }

  // MtScheduler Unique

  public void setTimeout(int timeout) {
    this.timeout = timeout;
  }

  public void setVpi(Vpi vpi) {
    this.vpi = vpi;
  }
}
