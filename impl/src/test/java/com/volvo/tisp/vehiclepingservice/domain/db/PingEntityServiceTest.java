package com.volvo.tisp.vehiclepingservice.domain.db;

import java.time.Instant;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.database.repository.PingEntityRepository;

import reactor.core.publisher.Mono;

@ExtendWith(SpringExtension.class)
class PingEntityServiceTest {

  @Mock
  private PingEntityRepository pingEntityRepository;

  @InjectMocks
  private PingEntityService pingEntityService;

  @Test
  void countShouldReturnCorrectCount() throws ExecutionException, InterruptedException {
    Mockito.when(pingEntityRepository.count()).thenReturn(Mono.just(5L));

    CompletableFuture<Long> result = pingEntityService.count();

    Assertions.assertEquals(5L, result.get());
    Mockito.verify(pingEntityRepository).count();
  }

  @Test
  void countWhenErrorOccursShouldHandleGracefully() {
    RuntimeException expectedException = new RuntimeException("Database error");
    Mockito.when(pingEntityRepository.count()).thenReturn(Mono.error(expectedException));

    Exception exception = Assertions.assertThrows(Exception.class, () -> pingEntityService.count().join());
    Assertions.assertInstanceOf(RuntimeException.class, exception.getCause());
    Assertions.assertEquals("Database error", exception.getCause().getMessage());
  }

  @Test
  void findByCorrelationIdWhenExistsShouldReturnEntity() throws ExecutionException, InterruptedException {
    String correlationId = "test-correlation-id";
    PingEntity expectedEntity = createTestPingEntity(correlationId);
    Mockito.when(pingEntityRepository.findByCorrelationId(correlationId))
        .thenReturn(Mono.just(expectedEntity));

    PingEntity result = pingEntityService.findByCorrelationId(correlationId).get();

    Assertions.assertNotNull(result);
    Assertions.assertEquals(correlationId, result.getCorrelationId());
    Mockito.verify(pingEntityRepository).findByCorrelationId(correlationId);
  }

  @Test
  void findByCorrelationIdWhenNotFoundShouldReturnNull() throws ExecutionException, InterruptedException {
    String correlationId = "non-existent";
    Mockito.when(pingEntityRepository.findByCorrelationId(correlationId))
        .thenReturn(Mono.empty());

    PingEntity result = pingEntityService.findByCorrelationId(correlationId).get();

    Assertions.assertNull(result);
    Mockito.verify(pingEntityRepository).findByCorrelationId(correlationId);
  }

  @Test
  void findByMessageIdWhenExistsShouldReturnEntity() throws ExecutionException, InterruptedException {
    Long messageId = 123L;
    PingEntity expectedEntity = createTestPingEntity(messageId);
    Mockito.when(pingEntityRepository.findByMessageId(messageId))
        .thenReturn(Mono.just(expectedEntity));

    PingEntity result = pingEntityService.findByMessageId(messageId).get();

    Assertions.assertNotNull(result);
    Assertions.assertEquals(messageId, result.getMessageId());
    Mockito.verify(pingEntityRepository).findByMessageId(messageId);
  }

  @Test
  void removeAllWhenErrorOccursShouldHandleGracefully() {
    RuntimeException expectedException = new RuntimeException("Delete failed");
    Mockito.when(pingEntityRepository.deleteAll()).thenReturn(Mono.error(expectedException));

    Exception exception = Assertions.assertThrows(Exception.class, () -> pingEntityService.removeAll().join());
    Assertions.assertInstanceOf(RuntimeException.class, exception.getCause());
    Assertions.assertEquals("Delete failed", exception.getCause().getMessage());
  }

  @Test
  void removeAll_ShouldDeleteAllEntities() throws ExecutionException, InterruptedException {
    Mockito.when(pingEntityRepository.deleteAll()).thenReturn(Mono.empty());

    pingEntityService.removeAll().get();

    Mockito.verify(pingEntityRepository).deleteAll();
  }

  @Test
  void upsertWhenNullEntityShouldReturnNull() throws ExecutionException, InterruptedException {
    PingEntity result = pingEntityService.upsert(null).toCompletableFuture().get();

    Assertions.assertNull(result);
    Mockito.verify(pingEntityRepository, Mockito.never()).save(Mockito.any());
  }

  @Test
  void upsertWhenSaveErrorsShouldHandleGracefully() {
    PingEntity entity = createTestPingEntity("test-correlation-id");
    RuntimeException expectedException = new RuntimeException("Save failed");
    Mockito.when(pingEntityRepository.save(entity)).thenReturn(Mono.error(expectedException));

    Exception exception = Assertions.assertThrows(Exception.class,
        () -> pingEntityService.upsert(entity).toCompletableFuture().join());
    Assertions.assertInstanceOf(RuntimeException.class, exception.getCause());
    Assertions.assertEquals("Save failed", exception.getCause().getMessage());
  }

  @Test
  void upsertWhenValidEntityShouldSaveEntity() throws ExecutionException, InterruptedException {
    PingEntity entity = createTestPingEntity("test-correlation-id");
    Mockito.when(pingEntityRepository.save(entity)).thenReturn(Mono.just(entity));

    PingEntity result = pingEntityService.upsert(entity).toCompletableFuture().get();

    Assertions.assertNotNull(result);
    Assertions.assertEquals(entity, result);
    Mockito.verify(pingEntityRepository).save(entity);
  }

  private PingEntity createTestPingEntity(String correlationId) {
    PingEntity entity = new PingEntity();
    entity.setId("test-id");
    entity.setCorrelationId(correlationId);
    entity.setCreatedAt(Instant.now());
    entity.setMessageId(123L);
    entity.setStatus(Status.SUCCESS);
    return entity;
  }

  private PingEntity createTestPingEntity(Long messageId) {
    PingEntity entity = new PingEntity();
    entity.setId("test-id");
    entity.setCorrelationId("test-correlation-id");
    entity.setCreatedAt(Instant.now());
    entity.setMessageId(messageId);
    entity.setStatus(Status.SUCCESS);
    return entity;
  }
}
