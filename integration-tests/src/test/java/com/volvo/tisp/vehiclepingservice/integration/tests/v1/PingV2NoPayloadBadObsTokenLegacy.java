package com.volvo.tisp.vehiclepingservice.integration.tests.v1;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.springframework.stereotype.Component;
import org.springframework.test.web.reactive.server.WebTestClient;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.jwk.ECKey;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.test.jms.TestJmsClient;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.IdpMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.Idpm2mMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.JWKSClientTestMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.StateRepositoryRestMockExtended;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.LegacyOutput;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.RestUtil;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;

@Component
public class PingV2NoPayloadBadObsTokenLegacy {
  public static final String STATE_TIMEOUT_QUEUE = "LOCAL.LOCAL.LOCAL.VPS.STATE_TIMEOUT";

  private final JWKSClientTestMock jWKSClientTestMock;
  private final TestJmsClient jmsClient;
  private final MtMessagePublisher mtMessagePublisher;
  private final PingEntityService pingEntityService;
  private final ServiceAccessTokenService serviceAccessTokenService;
  private final StateRepositoryRestMockExtended stateRepositoryRestMock;
  private final WebTestClient webTestClient;

  public PingV2NoPayloadBadObsTokenLegacy(JWKSClientTestMock jWKSClientTestMock, TestJmsClient jmsClient, MtMessagePublisher mtMessagePublisher,
      PingEntityService pingEntityService, ServiceAccessTokenService serviceAccessTokenService, StateRepositoryRestMockExtended stateRepositoryRestMock,
      WebTestClient webTestClient) {
    this.jWKSClientTestMock = jWKSClientTestMock;
    this.jmsClient = jmsClient;
    this.mtMessagePublisher = mtMessagePublisher;
    this.pingEntityService = pingEntityService;
    this.serviceAccessTokenService = serviceAccessTokenService;
    this.stateRepositoryRestMock = stateRepositoryRestMock;
    this.webTestClient = webTestClient;
  }

  private static String createObsToken(int badType, ECKey ecKey) throws JOSEException {
    return switch (badType) {
      case 1 -> // expired token
          JWKSClientTestMock.getSignedJwtToken(ecKey, "mh.w svc.256", Instant.now().minus(60, ChronoUnit.SECONDS));
      case 2 -> // Not signed
          JWKSClientTestMock.getPlainJwtToken(ecKey, "mh.w svc.256", Instant.now().plus(60, ChronoUnit.SECONDS));
      case 3 -> // Bad scope claim
          JWKSClientTestMock.getSignedJwtToken(ecKey, "mh.w", Instant.now().plus(60, ChronoUnit.SECONDS));
      default -> null;
    };
  }

  private static void verifyDataApi(String vpi, LegacyOutput vehiclePingDataResponse) {
    Assertions.assertEquals(vpi, vehiclePingDataResponse.vpi());
    Assertions.assertNull(vehiclePingDataResponse.payloadSent());
    Assertions.assertNull(vehiclePingDataResponse.payloadReceived());
    Assertions.assertNotNull(vehiclePingDataResponse.startTime());
    Assertions.assertNotNull(vehiclePingDataResponse.stopTime());
    Assertions.assertFalse(vehiclePingDataResponse.success());
    Assertions.assertNotNull(vehiclePingDataResponse.errorReason());
    Assertions.assertEquals("Ping Timed out", vehiclePingDataResponse.errorReason());
  }

  public void test(int badType) throws Exception {
    mtMessagePublisher.setMtDispatcherFeature(true);
    serviceAccessTokenService.setServiceAccessTokenValidationEnabled(true);

    Idpm2mMock.mockCallToFetchServiceAccessToken("mh.w svc.256", "clientId");
    ECKey ecKey = jWKSClientTestMock.createECKey();
    IdpMock.mockKeyQuery(ecKey);

    String vpi = TestUtil.stubTusSubscription(2);

    String obsToken = createObsToken(badType, ecKey);

    stateRepositoryRestMock.stubPush(Instant.now().plus(3, ChronoUnit.MINUTES), 200);

    String correlationId = RestUtil.verifyGetHttpResponse(webTestClient, "/vehicleping/vpi/" + vpi, obsToken, String.class);

    PingEntity db = pingEntityService.findByCorrelationId(correlationId).get();
    TestUtil.verifyPingEntityPending(vpi, db);

    LegacyOutput getDataPending = RestUtil.verifyGetHttpResponse(webTestClient, "/vehicleping/data/correlationid/" + correlationId, obsToken,
        LegacyOutput.class);

    TestUtil.verifyGetDataApiPending(vpi, getDataPending);
    timeOutState(correlationId);
    stateRepositoryRestMock.stubPop(correlationId, List.of(correlationId));

    Awaitility.await()
        .atMost(Duration.of(10, ChronoUnit.SECONDS))
        .until(() -> pingEntityService.findByCorrelationId(correlationId).get().getStatus() != Status.PENDING);

    PingEntity pingEntityFinalDb = pingEntityService.findByCorrelationId(correlationId).get();
    TestUtil.verifyPingEntity(Status.TIMEOUT, pingEntityFinalDb, vpi);

    LegacyOutput vehiclePingDataResponse = RestUtil.verifyGetHttpResponse(webTestClient, "/vehicleping/data/correlationid/" + correlationId, obsToken,
        LegacyOutput.class);
    verifyDataApi(vpi, vehiclePingDataResponse);

    IdpMock.verifyIdpWiremockTokenInteractions(1);
  }

  private void timeOutState(String refId) {
    TispContext.runInContext(() ->
        jmsClient
            .newMessage()
            .payload(refId)
            .messageType("statetimeout.txt")
            .messageVersion("1.0")
            .sendTo(STATE_TIMEOUT_QUEUE)
    );
  }
}
