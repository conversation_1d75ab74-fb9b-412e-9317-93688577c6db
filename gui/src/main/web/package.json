{"name": "Vehicle-Ping-Service", "version": "0.1.0", "private": true, "description": "", "scripts": {"clean-install": "rm -rf node_modules && yalc remove support-tool-components && yalc add support-tool-components && yarn install", "start": "webpack-dev-server --inline --progress --port 8082", "build": "webpack --config config/webpack.prod.js --progress --profile --bail", "lint": "tslint src/**/*.ts -t verbose"}, "dependencies": {"@fortawesome/fontawesome": "1.2.0-6", "@fortawesome/fontawesome-common-types": "0.1.3", "@fortawesome/free-solid-svg-icons": "^5.8.1", "@fortawesome/react-fontawesome": "0.1.0-3", "@types/react": "^16.9.9", "@types/react-dom": "^16.9.2", "bootstrap": "4.3.1", "create-react-class": "15.6.3", "node-sass": "4.12.0", "react": "16.8.6", "react-bootstrap": "1.0.0-beta.9", "react-dom": "16.8.6", "react-table": "6.8.6", "react-tap-event-plugin": "3.0.3", "styled-components": "3.4.6", "support-tool-components": "file:.yalc/support-tool-components", "sync-request": "^6.1.0", "yarn": "1.15.2"}, "devDependencies": {"@craco/craco": "^5.5.0", "@semantic-ui-react/craco-less": "^1.1.0", "@types/node": "8.10.36", "@types/react-bootstrap-typeahead": "3.4.3", "@types/react-table": "6.7.13", "awesome-typescript-loader": "3.2.3", "core-js": "2.5.1", "css-loader": "0.28.7", "extract-text-webpack-plugin": "^3.0.2", "file-loader": "1.1.5", "html-loader": "0.5.1", "html-webpack-plugin": "2.30.1", "less-loader": "^5.0.0", "null-loader": "0.1.1", "postcss-loader": "^3.0.0", "raw-loader": "0.5.1", "rimraf": "2.6.2", "sass-loader": "7.1.0", "semantic-ui-less": "^2.4.1", "semantic-ui-react": "^0.88.1", "source-map-loader": "0.2.4", "style-loader": "0.23.0", "tslint": "5.11.0", "typescript": "3.1.3", "uglifyjs-webpack-plugin": "1.2.6", "url-loader": "^2.1.0", "webpack": "3.12.0", "webpack-dev-server": "2.9.3", "webpack-merge": "4.1.4", "zone.js": "0.8.26"}, "postcss": {}}