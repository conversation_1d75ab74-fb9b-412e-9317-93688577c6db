package com.volvo.tisp.vehiclepingservice.metrics;

import org.junit.jupiter.api.Test;

import io.micrometer.core.instrument.Tags;

class MtDispatcherMetricReporterTest {
  @Test
  void onPublishFailure() {
    MetricsReporterTestUtils.initReporterAndTest(MtDispatcherMetricReporter::new, (registry, reporter) -> {
      reporter.onPublishFailure();
      MetricsReporterTestUtils.checkCounter(registry, 1, MtDispatcherMetricReporter.MT_DISPATCHER, Tags.of(MtDispatcherMetricReporter.TYPE, "failure"));
    });
  }

  @Test
  void onPublishNoSubscriber() {
    MetricsReporterTestUtils.initReporterAndTest(MtDispatcherMetricReporter::new, (registry, reporter) -> {
      reporter.onPublishNoSubscriber();
      MetricsReporterTestUtils.checkCounter(registry, 1, MtDispatcherMetricReporter.MT_DISPATCHER, Tags.of(MtDispatcherMetricReporter.TYPE, "no-subscriber"));
    });
  }

  @Test
  void onPublishSuccess() {
    MetricsReporterTestUtils.initReporterAndTest(MtDispatcherMetricReporter::new, (registry, reporter) -> {
      reporter.onPublishSuccess();
      MetricsReporterTestUtils.checkCounter(registry, 1, MtDispatcherMetricReporter.MT_DISPATCHER, Tags.of(MtDispatcherMetricReporter.TYPE, "success"));
    });
  }
}