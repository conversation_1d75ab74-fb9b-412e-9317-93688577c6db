package com.volvo.tisp.vehiclepingservice.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import com.volvo.tisp.vehiclepingservice.rest.model.Channel;

class TimeoutCalculatorTest {

  @ParameterizedTest
  @CsvSource({
      "1, UDP",
      "2, SMS",
      "3, SAT"
  })
  void getDefaultTimeoutTest(int expectedTimeout, String channel) {
    int timeout = new TimeoutCalculator(1, 2, 3).getDefaultTimeout(Channel.fromValue(channel));
    Assertions.assertEquals(expectedTimeout, timeout);
  }
}
