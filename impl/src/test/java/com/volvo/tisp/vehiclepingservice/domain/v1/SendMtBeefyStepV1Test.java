package com.volvo.tisp.vehiclepingservice.domain.v1;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;
import com.volvo.tisp.vehiclepingservice.util.TestUtils;

@ExtendWith(MockitoExtension.class)
class SendMtBeefyStepV1Test {

  @Mock
  private MtMessagePublisher mtMessagePublisher;
  @Mock
  private PingCoder pingCoder;
  @InjectMocks
  private SendMtBeefyStepV1 sendMtBeefyStep;
  @Mock
  private StateRepository stateClient;

  @Test
  void getPayloadShouldEncodeBeefyPing() {
    String body = new String(TestUtils.PAYLOAD);
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), false, body, null, 0);
    long swapCorrelationId = 12345L;

    byte[] expectedEncodedPayload = "encoded payload".getBytes();
    Mockito.when(pingCoder.encodeBeefyPing(swapCorrelationId, false, body)).thenReturn(expectedEncodedPayload);

    byte[] result = sendMtBeefyStep.getPayload(requestData, swapCorrelationId);

    Mockito.verify(pingCoder).encodeBeefyPing(swapCorrelationId, false, body);
    Assertions.assertArrayEquals(expectedEncodedPayload, result);
  }

  @Test
  void getPayloadShouldHandleEncodingException() {
    String body = new String(TestUtils.PAYLOAD);
    long swapCorrelationId = 12345L;
    RequestData requestData = new RequestData(TestUtils.VPI.toString(), false, body, null, 0);

    Mockito.when(pingCoder.encodeBeefyPing(swapCorrelationId, false, body)).thenThrow(new RuntimeException("Encoding failed"));

    Assertions.assertThrows(RuntimeException.class, () -> sendMtBeefyStep.getPayload(requestData, swapCorrelationId));
    Mockito.verify(pingCoder).encodeBeefyPing(swapCorrelationId, false, body);
  }
}

