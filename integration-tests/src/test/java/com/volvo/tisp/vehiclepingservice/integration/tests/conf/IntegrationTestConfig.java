package com.volvo.tisp.vehiclepingservice.integration.tests.conf;

import java.time.Duration;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.test.web.reactive.server.WebTestClient;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.framework.jms.DestinationNamingConvention;
import com.volvo.tisp.framework.test.jms.QueueConsumer;
import com.volvo.tisp.framework.test.jms.TestJmsClient;
import com.volvo.tisp.vehiclepingservice.domain.v1.PingCoder;
import com.volvo.tisp.vehiclepingservice.domain.v2.PingV2Coder;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.JWKSClientTestMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.MtDispatcherMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.StateRepositoryRestMockExtended;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.TceMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;

import io.netty.handler.logging.LogLevel;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.logging.AdvancedByteBufFormat;

@TestConfiguration(proxyBeanMethods = false)
public class IntegrationTestConfig {
  @PostConstruct
  public void initWireMock() {
    TestUtil.setupMockConfiguration();
    TestUtil.stubAuth();
    TestUtil.stubSubr();
  }

  @Bean
  public JWKSClientTestMock jWKSClientTestUtil(Environment environment) {
    return new JWKSClientTestMock(environment);
  }

  @Bean
  public MtDispatcherMock mtDispatcherMock(TestJmsClient jmsClient, ObjectMapper objectMapper, PingCoder pingCoder, PingV2Coder pingCoderV2,
      QueueConsumer queueConsumer, TceMock tceMock) {
    return new MtDispatcherMock(jmsClient, objectMapper, pingCoder, pingCoderV2, queueConsumer, tceMock);
  }

  @Bean
  public StateRepositoryRestMockExtended stateRepositoryRestMock(
      DestinationNamingConvention destinationNamingConvention) {
    return new StateRepositoryRestMockExtended(destinationNamingConvention);
  }

  @Bean
  public TceMock tceMock(TestJmsClient jmsClient, PingCoder pingCoder, PingV2Coder pingCoderV2, QueueConsumer queueConsumer) {
    return new TceMock(jmsClient, pingCoder, pingCoderV2, queueConsumer);
  }

  @Bean
  public WebTestClient webTestClient(@Value("${server.port}") int serverPort) {
    ReactorClientHttpConnector connector = new ReactorClientHttpConnector(
        HttpClient.create()
            .wiretap("web.test.client", LogLevel.INFO, AdvancedByteBufFormat.TEXTUAL)
    );

    return WebTestClient.bindToServer(connector)
        .baseUrl("http://127.0.0.1:" + serverPort)
        .responseTimeout(Duration.ofMinutes(10))
        .build();
  }
}