package com.volvo.tisp.vehiclepingservice.util;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.idpm2m.client.JWKSClient;
import com.volvo.tisp.idpm2m.client.model.UnauthorizedTokenException;
import com.volvo.tisp.vehiclepingservice.metrics.ServiceAccessTokenMetricReporter;

@Component
public class ServiceAccessTokenService {
  static final List<String> IDS = List.of("256");
  static final List<String> RAW_SCOPE_LIST = List.of("mh.w");
  static final List<Integer> SERVICE_ID_SCOPE_LIST = List.of(256);
  private static final Logger logger = LoggerFactory.getLogger(ServiceAccessTokenService.class);

  private final ServiceAccessTokenClient client;
  private final Optional<JWKSClient> jWKSClient;
  private final ServiceAccessTokenMetricReporter serviceAccessTokenMetricReporter;
  private boolean serviceAccessTokenValidationEnabled;

  public ServiceAccessTokenService(ServiceAccessTokenClient client, Optional<JWKSClient> jwksClient,
      @Value("${service.token.validation:true}") boolean serviceAccessTokenValidationEnabled,
      ServiceAccessTokenMetricReporter serviceAccessTokenMetricReporter) {
    this.client = client;
    this.jWKSClient = jwksClient;
    this.serviceAccessTokenValidationEnabled = serviceAccessTokenValidationEnabled;
    this.serviceAccessTokenMetricReporter = serviceAccessTokenMetricReporter;
  }

  public CompletableFuture<String> getServiceAccessToken() {
    logger.info(Retention.SHORT, "Token null or expired, retrieving new Token");
    serviceAccessTokenMetricReporter.onTokenRequest();
    return client.fetchServiceAccessToken(SERVICE_ID_SCOPE_LIST, RAW_SCOPE_LIST);
  }

  /**
   * Used for tests
   */
  public boolean isServiceAccessTokenValidationEnabled() {
    return serviceAccessTokenValidationEnabled;
  }

  /**
   * Used for tests
   */
  public synchronized void setServiceAccessTokenValidationEnabled(boolean enabled) {
    this.serviceAccessTokenValidationEnabled = enabled;
  }

  public void validateToken(String token) throws UnauthorizedTokenException {
    if (serviceAccessTokenValidationEnabled) {
      if (jWKSClient.isEmpty()) {
        logger.error("there is no jWKSClient configured, discarding the incoming mo message");
        serviceAccessTokenMetricReporter.onValidateFailure();
        return;
      }
      try {
        this.jWKSClient.get().authorizeOBSServiceAccessToken(token, IDS);
        serviceAccessTokenMetricReporter.onValidateSuccess();
      } catch (UnauthorizedTokenException e) {
        serviceAccessTokenMetricReporter.onValidateFailure();
        throw e;
      }
    }
  }
}
