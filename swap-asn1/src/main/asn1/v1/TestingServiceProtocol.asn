--
-- This file defines the ASN.1 notation of the
-- Test Service Data Units used by the
-- DynaFleet application
-- Revision: 1.11
--
TestServiceProtocol DEFINITIONS AUTOMATIC TAGS ::=
BEGIN
serviceID INTEGER ::= 256
serviceVersion INTEGER ::= 1
-- Messages
TestServicePdu ::= CHOICE {
          ping                    Ping,
          pong                    Pong,
          autoPingSetup           AutoPingSetup,
          autoPingResponse        AutoPingResponse,
          beefyMessage            BeefyMessage,
          beefyMessageResponse    BeefyMessageResponse,
          reserved06              NULL,
          reserved07              NULL,
          reserved08              NULL,
          reserved09              NULL,
          reserved10              NULL,
          reserved11              NULL,
          reserved12              NULL,
          reserved13              NULL,
          reserved14              NULL,
          reserved15              NULL,
          reserved16              NULL,
          reserved17              NULL,
          reserved18              NULL,
          reserved19              NULL,
          reserved20              NULL,
          reserved21              NULL,
          reserved22              NULL,
          reserved23              NULL,
          reserved24              NULL,
          reserved25              NULL,
          reserved26              NULL,
          reserved27              NULL,
          reserved28              NULL,
          reserved29              NULL,
          reserved30              NULL,
          reserved31              NULL
}
        -- Ping may be sent in any direction, may result in a Pong response
        Ping ::= SEQUENCE {
          -- true if the ping should result in a Pong
          responseExpected BOOLEAN,
          -- the id is used to correlate a request with a response
          id INTEGER (0..4294967295) OPTIONAL
        }
        -- Response to a Ping
        Pong ::= SEQUENCE {
          -- the id should be set to the same value as received in the Ping
          id INTEGER (0..4294967295) OPTIONAL
        }
        -- AutoPingSetup may be sent in any direction, results in Ping being sent
        -- with specified interval. It's up to the implementation to decide if
        -- AutoPingSetup's are persistent. A new AutoPingSetup disables any already
        -- running AutoPing.
        AutoPingSetup ::= SEQUENCE {
          -- the delay between each Ping in seconds
          interval INTEGER (0..65335),
          -- the number of pings to be sent, function is disabled if value is zero
          count INTEGER (0..65335),
          -- set the expectedResponse flag in Ping messages
          pingResponseExpected BOOLEAN,
          -- the id is used to correlate the AutoPingResponse
          id INTEGER (0..4294967295) OPTIONAL
        }
        -- Response to an AutoPingSetup message
        AutoPingResponse ::= SEQUENCE {
          -- the id should be set to the same value as received in the AutoPingSetup
          id INTEGER (0..4294967295) OPTIONAL
        }
        -- A BeefyMessage is a large message used mainly for testing segmentation
        -- and re-assembly.
        BeefyMessage ::= SEQUENCE {
          -- true if the message should result in a response
          responseExpected BOOLEAN,
          -- true if the payload of this message should be copied to the response
          copyPayload BOOLEAN,
          -- specifies how many bytes the payload in the response should contain
          responseSize INTEGER (0..65335),
          -- the id is used to correlate the response
          id INTEGER (0..4294967295) OPTIONAL,
          -- payload
          payload OCTET STRING (SIZE(0..65335))
        }
        -- Response to a BeefyMessage. The payload is filled with random data to
        -- 'responseSize' bytes as specified in the request.
        BeefyMessageResponse ::= SEQUENCE {
          -- the id should be set to the same value as received in the setup
          id INTEGER (0..4294967295) OPTIONAL,
          -- may be used to send status information of any kind
          status OCTET STRING (SIZE(0..255)),
          -- payload
          payload OCTET STRING (SIZE(0..65335))
        }
END
