package com.volvo.tisp.vehiclepingservice.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.volvo.tisp.vehiclepingservice.rest.model.Channel;

public class ChannelUtil {

  private static final Logger logger = LoggerFactory.getLogger(ChannelUtil.class);

  private ChannelUtil() {
    throw new IllegalStateException("Utility class");
  }

  public static Channel calculateResponseChannel(String receivingChannel) {
    if (!StringUtils.hasLength(receivingChannel) || "UDP".equalsIgnoreCase(receivingChannel)) {
      return Channel.UDP;
    }
    if ("SATELLITE".equalsIgnoreCase(receivingChannel)) {
      return Channel.SAT;
    } else if ("SMS".equalsIgnoreCase(receivingChannel)) {
      return Channel.SMS;
    } else {
      logger.warn("Unknown receiving channel: {}, defaulting to UDP", receivingChannel);
      return Channel.UDP;
    }
  }
}
