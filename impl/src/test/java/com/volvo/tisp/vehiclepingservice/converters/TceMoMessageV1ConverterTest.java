package com.volvo.tisp.vehiclepingservice.converters;

import static org.mockito.Mockito.mock;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.PingCoder;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.SrpOption;

public class TceMoMessageV1ConverterTest {
  private static final String HANDLE = "someHandle";
  private static final String PAYLOAD = "payload";
  private static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");

  public static MoMessage createMoMessage(int serviceVersion) {
    SrpOption srpOption = new SrpOption();
    srpOption.setDstService(256);
    srpOption.setDstVersion(serviceVersion);

    MoMessage moMessage = new MoMessage();
    moMessage.setPayload(PAYLOAD.getBytes());
    moMessage.setVehiclePlatformId(VPI.toString());
    moMessage.setHandle(HANDLE);
    moMessage.setSrpOption(srpOption);

    return moMessage;
  }

  @Test
  void convertWitValidInputTest() {
    PingCoder mockPingCoder = mock(PingCoder.class);
    TceMoMessageV1Converter converter = new TceMoMessageV1Converter(mockPingCoder);

    MoMessage mockMoMessage = createMoMessage(256);
    DecodedV1 mockDecoded = mock(DecodedV1.class);
    Mockito.when(mockPingCoder.decode(PAYLOAD.getBytes())).thenReturn(mockDecoded);

    MoDtoV1 result = converter.convert(mockMoMessage, "testCorrelationId");

    Assertions.assertEquals(VPI, result.getVpi());
    Assertions.assertEquals("testCorrelationId", result.getCorrelationId());
    Assertions.assertEquals(mockDecoded, result.getDecoded());
  }

  @Test
  void tceMoMessageV1ConverterConstructorTest() {
    Assertions.assertNotNull(new TceMoMessageV1Converter(new PingCoder()));
  }
}
