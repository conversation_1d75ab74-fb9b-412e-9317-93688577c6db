package com.volvo.tisp.vehiclepingservice.util;

import static com.volvo.tisp.vehiclepingservice.util.TokenFetcher.SECRET_PROPERTY_NAME;

import java.io.Closeable;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.security.oauth2.core.endpoint.DefaultMapOAuth2AccessTokenResponseConverter;
import org.springframework.security.oauth2.core.endpoint.OAuth2AccessTokenResponse;
import org.springframework.web.reactive.function.client.WebClient;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.Slf4jNotifier;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.wirelesscar.config.mock.MockConfiguration;

class TokenFetcherTest {
  private static final String REQUEST_PATH = "/oauth2/token";

  private static WireMockServerWrapper createAndStartWireMockServer() {
    WireMockServer wireMockServer = createWireMockServer();
    wireMockServer.start();

    return new WireMockServerWrapper(wireMockServer);
  }

  private static WireMockServer createWireMockServer() {
    WireMockConfiguration wireMockConfiguration = new WireMockConfiguration().dynamicPort().notifier(new Slf4jNotifier(true));
    return new WireMockServer(wireMockConfiguration);
  }

  private static MockConfiguration getEmptyMockConfiguration() {
    MockConfiguration mockConfiguration = MockConfiguration.getConfig();
    mockConfiguration.deleteAllProperties();
    return mockConfiguration;
  }

  @Test
  void postAccessTokenRequestTest() throws JsonProcessingException {
    MockConfiguration mockConfiguration = getEmptyMockConfiguration();
    mockConfiguration.setPropertySpecific(mockConfiguration.getComponentShortName(), SECRET_PROPERTY_NAME, "secret");

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;

      String token = "token";
      Map<String, String> responseMap = Map.of("access_token", token, "token_type", "BEARER");

      ResponseDefinitionBuilder responseDefinitionBuilder = WireMock.ok()
          .withHeader("Content-Type", "application/json")
          .withBody(new ObjectMapper().writeValueAsString(responseMap));

      wireMockServer.stubFor(WireMock.post(REQUEST_PATH).willReturn(responseDefinitionBuilder));

      WebClient webClient = WebClient.create(wireMockServer.baseUrl());
      TokenFetcher tokenFetcher = new TokenFetcher(webClient, new DefaultMapOAuth2AccessTokenResponseConverter());

      OAuth2AccessTokenResponse response = tokenFetcher.postAccessTokenRequest("").join();

      Assertions.assertEquals(token, response.getAccessToken().getTokenValue());
    }
  }

  private record WireMockServerWrapper(WireMockServer wireMockServer) implements Closeable {
    @Override
    public void close() {
      wireMockServer.stop();
    }
  }
}
