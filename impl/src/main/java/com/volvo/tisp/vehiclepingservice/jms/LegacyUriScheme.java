package com.volvo.tisp.vehiclepingservice.jms;

import static java.util.Locale.ENGLISH;

import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;

/**
 * Legacy, copied from CB, TODO: replace when we have a better solution
 */
@Deprecated(since = "2021-11-16", forRemoval = true)
public class LegacyUriScheme {
  private static final Config cfg = ConfigFactory.getConfig();

  public static String createJMSDestinationName(final String postfix) {
    return createJMSDestinationName(postfix, cfg.getComponentShortRuntimeName());
  }

  public static String createJMSDestinationName(final String postfix, final String targetComponentShortName) {
    return String.format(ENGLISH, "%s.%s.%s.%s.%s", cfg.getSolution(), cfg.getSite(), cfg.getEnvironmentId(), targetComponentShortName, postfix)
        .toUpperCase(ENGLISH);
  }
}
