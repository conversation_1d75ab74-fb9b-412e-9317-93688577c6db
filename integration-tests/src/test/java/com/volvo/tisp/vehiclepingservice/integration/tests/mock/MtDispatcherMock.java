package com.volvo.tisp.vehiclepingservice.integration.tests.mock;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.Base64;
import java.util.UUID;

import jakarta.jms.JMSException;
import jakarta.jms.Message;

import org.junit.jupiter.api.Assertions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.test.jms.QueueConsumer;
import com.volvo.tisp.framework.test.jms.TestJmsClient;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtStatus;
import com.volvo.tisp.vc.mt.message.client.json.v1.Status;
import com.volvo.tisp.vehiclepingservice.domain.v1.PingCoder;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.DecodedV1;
import com.volvo.tisp.vehiclepingservice.domain.v2.PingV2Coder;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.integration.tests.utils.TestUtil;
import com.volvo.tisp.vehiclepingservice.jms.MoMessageJmsHandler;
import com.volvo.tisp.vehiclepingservice.jms.MtStatusJmsHandler;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessage;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.BeefyMessageResponse;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Ping;
import com.volvo.tisp.vehiclepingservice.swap.v1.ping.Pong;
import com.volvo.tisp.vehiclepingservice.util.ChecksumCalculator;
import com.volvo.tisp.vps.api.PingRequest;
import com.volvo.tisp.vps.api.PingResponse;
import com.volvo.tisp.vps.api.TestService;
import com.wirelesscar.tce.api.v2.MoMessage;
import com.wirelesscar.tce.api.v2.MtStatusMessage;

public class MtDispatcherMock {
  public static final String MO_MESSAGE_IN_QUEUE = TestUtil.QUEUE_PREFIX + MoMessageJmsHandler.MO_MESSAGE_IN_QUEUE;
  public static final String MT_QUEUE = TestUtil.QUEUE_PREFIX + "MT.NEW.IN";
  public static final String MT_STATUS_IN_QUEUE = TestUtil.QUEUE_PREFIX + MtStatusJmsHandler.MT_STATUS_IN_QUEUE;
  private static final Logger logger = LoggerFactory.getLogger(MtDispatcherMock.class);

  private final TestJmsClient jmsClient;
  private final ObjectMapper objectMapper;
  private final PingCoder pingCoder;
  private final PingV2Coder pingCoderV2;
  private final QueueConsumer queueConsumer;
  private final TceMock tceMock;

  public MtDispatcherMock(TestJmsClient jmsClient, ObjectMapper objectMapper, PingCoder pingCoder, PingV2Coder pingCoderV2, QueueConsumer queueConsumer,
      TceMock tceMock) {
    this.jmsClient = jmsClient;
    this.objectMapper = objectMapper;
    this.pingCoder = pingCoder;
    this.pingCoderV2 = pingCoderV2;
    this.queueConsumer = queueConsumer;
    this.tceMock = tceMock;
  }

  private static MoDtoV2 createMoDtoV2(MtMessage mtMessage, TestService decoded) {
    MoDtoV2 dto = new MoDtoV2();
    dto.setDecoded(decoded);
    dto.setVpi(Vpi.ofString(mtMessage.getVehiclePlatformId()));
    return dto;
  }

  private static com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage createMoMessage(String vpi, String responseBase64Payload, int serviceVersion,
      String obsToken) {
    return new com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage()
        .withVehiclePlatformId(vpi)
        .withPayload(responseBase64Payload)
        .withServiceId(256)
        .withServiceVersion(serviceVersion)
        .withServiceAccessToken(obsToken)
        .withTrackingId(TrackingIdentifier.create().toString())
        .withOnboardTimestamp(Instant.now().toEpochMilli());
  }

  private static MtStatus createMtStatus(String correlationId, String vpi, Status status) {
    MtStatus mtStatusMessage = new MtStatus();
    mtStatusMessage.withVehiclePlatformId(vpi);
    mtStatusMessage.withCorrelationId(correlationId);
    mtStatusMessage.withStatus(status);
    mtStatusMessage.withTrackingId(TrackingIdentifier.create().toString());
    return mtStatusMessage;
  }

  private static void serviceFunctionValidation(Channel channel, MtMessage mtMessage) {
    // VC ServiceFunction validation, see ServiceFunctionCalculator class
    String serviceFunction = mtMessage.getServiceFunction();
    switch (channel) {
      case UDP -> assertEquals("ping-udp-only", serviceFunction);
      case SAT -> assertEquals("ping-sat-only", serviceFunction);
      case SMS -> assertEquals("ping-sms-only", serviceFunction);
    }
  }

  public void checkAndReplyBeefy(boolean useMoRouter, String token) {
    MtMessage mtMessage = receiveAndVerifyAccessToken();
    replyMtStatus(mtMessage, useMoRouter);

    if (useMoRouter) {
      moRouterReplyBeefyV1(mtMessage, token);
    } else {
      tceReplyBeefyV1(mtMessage);
    }
  }

  public void checkAndReplyPongV1(boolean useMoRouter, String token) {
    MtMessage mtMessage = receiveAndVerifyServiceAndToken(256, 1, true);
    logger.info("Received MtMessage with correlationId='{}' and VPI='{}'", mtMessage.getCorrelationId(), mtMessage.getVehiclePlatformId());

    replyMtStatus(mtMessage, useMoRouter);

    if (useMoRouter) {
      moRouterReplyPongV1(mtMessage, token);
    } else {
      tceReplyPongV1(mtMessage);
    }
  }

  public void checkAndReplyPongV2(boolean useMoRouter, String obsToken) {
    checkAndReplyPongV2(useMoRouter, obsToken, Channel.UDP, true);
  }

  public void checkAndReplyPongV2(boolean useMoRouter, String obsToken, Channel channel, boolean validChecksum) {
    MtMessage mtMessage = receiveAndVerifyServiceAndToken(256, 2, false);
    logger.info("Received MtMessage with correlationId='{}'", mtMessage.getCorrelationId());

    serviceFunctionValidation(channel, mtMessage);
    replyMtStatus(mtMessage, useMoRouter);

    if (useMoRouter) {
      moRouterReplyPongV2(mtMessage, obsToken, validChecksum);
    } else {
      tceReplyPongV2(mtMessage, validChecksum);
    }
  }

  public void checkBeefyMessageResponse(long id, String payload, boolean copyPayload, boolean useMoRouter) {
    MtMessage mtMessage = receiveMtDispatcherMessage();
    replyMtStatus(mtMessage, useMoRouter);

    logger.info("Received MtMessage with Vpi='{}'", mtMessage.getVehiclePlatformId());

    DecodedV1 decoded = decodeV1(mtMessage);

    Assertions.assertNotNull(decoded.getBeefyMessageResponse());

    BeefyMessageResponse beefyMessageResponse = decoded.getBeefyMessageResponse();

    if (copyPayload) {
      Assertions.assertEquals(id, beefyMessageResponse.getId());
      Assertions.assertArrayEquals(payload.getBytes(StandardCharsets.UTF_8), beefyMessageResponse.getPayload());
    } else {
      Assertions.assertNull(beefyMessageResponse.getPayload());
    }
  }

  public void checkPongV1(long id, boolean useMoRouter) {
    MtMessage mtMessage = receiveMtDispatcherMessage();
    logger.info("Received MtMessage with Vpi='{}'", mtMessage.getVehiclePlatformId());

    replyMtStatus(mtMessage, useMoRouter);

    DecodedV1 decoded = decodeV1(mtMessage);

    Assertions.assertNotNull(decoded.getPong());

    Pong pong = decoded.getPong();

    Assertions.assertEquals(id, pong.getId());
  }

  public void checkPongV2ToVehicle(UUID uuid, String payload, boolean useMoRouter) {
    MtMessage mtMessage = receiveAndVerifyServiceAndToken(256, 2, true);
    logger.info("Received MtMessage with correlationId='{}' and VPI='{}'", mtMessage.getCorrelationId(), mtMessage.getVehiclePlatformId());

    replyMtStatus(mtMessage, useMoRouter);

    TestService decoded = decodeV2(mtMessage);

    Assertions.assertEquals(uuid, decoded.getPingResponse().getCorrelationId());
    Assertions.assertEquals(PingResponse.Status.OK, decoded.getPingResponse().getStatus());
    if (payload == null) {
      Assertions.assertNull(decoded.getPingResponse().getPayload());
    } else {
      Assertions.assertEquals(payload, decoded.getPingResponse().getPayload().getPayloadValue());
    }
  }

  public void clear() {
    try {
      queueConsumer.purgeQueue(MT_QUEUE);
    } catch (JMSException e) {
      throw new RuntimeException(e);
    }
    tceMock.clear();
  }

  public void moRouterReplyMtStatus(String correlationId, String vpi, Status status) {
    logger.info("Sending MoRouter-MTStatus with correlationId='{}' and status='{}'", correlationId, status);

    MtStatus mtStatusMessage = createMtStatus(correlationId, vpi, status);
    sendMtStatusJmsMessage(mtStatusMessage, correlationId);
  }

  public MtMessage receiveMtDispatcherMessage() {
    Message msg = receiveRawMtDispatcherMessage();
    MtMessage mtMessage;

    try {
      String body = msg.getBody(String.class);
      mtMessage = objectMapper.readValue(body, MtMessage.class);
      return mtMessage;
    } catch (JMSException | JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  public Message receiveRawMtDispatcherMessage() {
    logger.info("Waiting for ping (mtDispatcher-format) -> vehicle...");

    try {
      Message message = queueConsumer.waitForMessage(MT_QUEUE, Duration.ofSeconds(15));
      Assertions.assertNotNull(message, "Received message should not be null");
      return message;
    } catch (JMSException e) {
      throw new RuntimeException(e);
    }
  }

  public long sendBeefy(String beefyPayload, boolean copyPayload, boolean useMoRouter, String token) {
    String vpi = TestUtil.createVehiclePlatformIdentifier();
    TestUtil.stubTus(vpi, 1);
    long id = randomMessageId();

    byte[] payload = pingCoder.encodeBeefyPing(id, copyPayload, beefyPayload);

    if (useMoRouter) {
      com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = createMoMessage(vpi, Base64.getEncoder().encodeToString(payload), 1, token);
      sendJmsMoRouterMoMessage(moMessage);
    } else {
      // TCE
      MoMessage moMessage = TestUtil.createMoMessage(payload, 1, vpi);
      tceMock.sendMoJmsMessage(moMessage);
    }
    return id;
  }

  public long sendPingV1(boolean useMoRouter, String obsToken) {
    String vpi = TestUtil.createVehiclePlatformIdentifier();
    TestUtil.stubTus(vpi, 1);
    long id = randomMessageId();

    byte[] payload = pingCoder.encodePing(id);

    if (useMoRouter) {
      com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = createMoMessage(vpi, Base64.getEncoder().encodeToString(payload), 1, obsToken);
      sendJmsMoRouterMoMessage(moMessage);
    } else {
      // TCE
      MoMessage moMessage = TestUtil.createMoMessage(payload, 1, vpi);
      tceMock.sendMoJmsMessage(moMessage);
    }
    return id;
  }

  public UUID sendPingV2(String payloadToSend, boolean copyPayload, boolean useMoRouter, String token) {
    String vpi = TestUtil.createVehiclePlatformIdentifier();
    TestUtil.stubTus(vpi, 2);
    UUID correlationId = UUID.randomUUID();

    byte[] payload = pingCoderV2.encodePing(correlationId, payloadToSend, copyPayload);

    if (useMoRouter) {
      com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = createMoMessage(vpi, Base64.getEncoder().encodeToString(payload), 2, token);
      sendJmsMoRouterMoMessage(moMessage);
    } else {
      // TCE
      MoMessage moMessage = TestUtil.createMoMessage(payload, 2, vpi);
      tceMock.sendMoJmsMessage(moMessage);
    }
    return correlationId;
  }

  public void tceReplyMtStatus(MtMessage mtMessage, String status) {
    Assertions.assertNotNull(mtMessage);
    tceReplyMtStatus(mtMessage.getCorrelationId(), mtMessage.getVehiclePlatformId(), status);
  }

  public void tceReplyMtStatus(String correlationId, String vpi, String status) {
    MtStatusMessage mtStatusMessage = TestUtil.createMtStatusMessage(correlationId, vpi, status);
    tceMock.sendMtStatusJmsMessage(mtStatusMessage, correlationId);
  }

  private DecodedV1 decodeV1(MtMessage mtMessage) {
    String base64Payload = mtMessage.getPayload();
    byte[] payload = Base64.getDecoder().decode(base64Payload);
    return pingCoder.decode(payload);
  }

  private TestService decodeV2(MtMessage mtMessage) {
    String base64Payload = mtMessage.getPayload();
    byte[] base64DecodedPayload = Base64.getDecoder().decode(base64Payload);
    return pingCoderV2.decode(base64DecodedPayload);
  }

  private void moRouterReplyBeefyV1(MtMessage mtMessage, String token) {
    DecodedV1 decoded = decodeV1(mtMessage);

    if (decoded.getBeefyMessage() != null) {
      BeefyMessage beefy = decoded.getBeefyMessage();

      if (beefy.getResponseExpected()) {
        byte[] encodedPong = pingCoder.encodeBeefyResponse(beefy.getId(), beefy.getPayload());
        String responseBase64Payload = Base64.getEncoder().encodeToString(encodedPong);
        com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = createMoMessage(mtMessage.getVehiclePlatformId(), responseBase64Payload, 1, token);
        logger.info("Sending MoRouter-MoResponse (BeefyResponse) dstService(256) with messageId='{}', vpi='{}'", beefy.getId(),
            moMessage.getVehiclePlatformId());
        sendJmsMoRouterMoMessage(moMessage);
      }
    }
  }

  private void moRouterReplyPongV1(MtMessage mtMessage, String token) {
    DecodedV1 decoded = decodeV1(mtMessage);

    if (decoded.getPing() != null) {
      Ping ping = decoded.getPing();

      if (ping.getResponseExpected()) {
        byte[] encodedPong = pingCoder.encodePong(ping.getId());
        String responseBase64Payload = Base64.getEncoder().encodeToString(encodedPong);
        com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = createMoMessage(mtMessage.getVehiclePlatformId(), responseBase64Payload, 1, token);
        logger.info("Sending MoRouter-MoResponse (Pong) dstService(256) with messageId='{}', vpi='{}'", ping.getId(), moMessage.getVehiclePlatformId());
        sendJmsMoRouterMoMessage(moMessage);
      }
    }
  }

  private void moRouterReplyPongV2(MtMessage mtMessage, String obsToken, boolean validChecksum) {
    TestService decoded = decodeV2(mtMessage);

    if (decoded.getPingRequest() != null) {
      PingRequest ping = decoded.getPingRequest();

      if (!validChecksum) {
        ping.getPayload().setChecksum(ChecksumCalculator.getSHA256Hash("RanomPayload"));
      }

      MoDtoV2 dto = createMoDtoV2(mtMessage, decoded);
      byte[] newPayload = pingCoderV2.encodePong(dto, null);
      String responseBase64Payload = Base64.getEncoder().encodeToString(newPayload);

      com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage = createMoMessage(mtMessage.getVehiclePlatformId(), responseBase64Payload, 2, obsToken);
      logger.info("Sending MoRouter-MoResponse (Pong) dstService(256.2) with correlationID='{}', vpi='{}'", ping.getCorrelationId(), dto.getVpi());
      sendJmsMoRouterMoMessage(moMessage);
    }
  }

  private long randomMessageId() {
    long temp = -1L;
    // 4294967295 is the max value for it in SWAP
    while (temp < 0 || temp > 4294967295L) {
      temp = (long) Math.abs((Math.random() * 4294967295L) - 1L);
    }
    return temp;
  }

  private MtMessage receiveAndVerifyAccessToken() {
    MtMessage mtMessage = receiveMtDispatcherMessage();
    logger.info("Received MtMessage with correlationId='{}'", mtMessage.getCorrelationId());

    Assertions.assertNotNull(mtMessage.getServiceAccessToken());

    return mtMessage;
  }

  private MtMessage receiveAndVerifyServiceAndToken(int expectedServiceId, int expectedServiceVersion, boolean requireVpi) {
    MtMessage mtMessage = receiveMtDispatcherMessage();
    logger.info("Received MtMessage with correlationId='{}'", mtMessage.getCorrelationId());

    Assertions.assertEquals(expectedServiceId, mtMessage.getServiceId());
    Assertions.assertEquals(expectedServiceVersion, mtMessage.getServiceVersion());
    if (requireVpi) {
      Assertions.assertNotNull(mtMessage.getVehiclePlatformId());
    }
    Assertions.assertNotNull(mtMessage.getServiceAccessToken());

    return mtMessage;
  }

  private void replyMtStatus(MtMessage mtMessage, boolean useMoRouter) {
    if (useMoRouter) {
      moRouterReplyMtStatus(mtMessage.getCorrelationId(), mtMessage.getVehiclePlatformId(), Status.DELIVERED);
    } else {
      tceReplyMtStatus(mtMessage.getCorrelationId(), mtMessage.getVehiclePlatformId(), Status.DELIVERED.toString());
    }
  }

  private void sendJmsMoRouterMoMessage(com.volvo.tisp.vc.mo.message.client.json.v1.MoMessage moMessage) {
    TispContext.runInContext(() ->
        jmsClient
            .newMessage()
            .payload(moMessage)
            .messageType(com.volvo.tisp.vc.mo.message.client.json.v1.MessageTypes.MO_MESSAGE)
            .messageVersion(com.volvo.tisp.vc.mo.message.client.json.v1.MessageTypes.VERSION_1_0)
            .sendTo(MO_MESSAGE_IN_QUEUE)
    );
  }

  private void sendMtStatusJmsMessage(MtStatus mtStatus, String correlationId) {
    TispContext.runInContext(() ->
        jmsClient
            .newMessage()
            .correlationId(correlationId)
            .payload(mtStatus)
            .messageType(MessageTypes.MT_STATUS)
            .messageVersion(MessageTypes.VERSION_1_0)
            .sendTo(MT_STATUS_IN_QUEUE)
    );
    logger.info("Send mt status, corrId='{}', vpi={}, status={}", correlationId, mtStatus.getVehiclePlatformId(), mtStatus.getStatus());
  }

  private void tceReplyBeefyV1(MtMessage mtMessage) {
    DecodedV1 decoded = decodeV1(mtMessage);

    if (decoded.getBeefyMessage() != null) {
      BeefyMessage beefy = decoded.getBeefyMessage();

      if (beefy.getResponseExpected()) {
        byte[] encodedPong = pingCoder.encodeBeefyResponse(beefy.getId(), beefy.getPayload());
        MoMessage moMessage = TestUtil.createMoMessage(encodedPong, 1, mtMessage.getVehiclePlatformId());
        logger.info("Sending TCE-MoResponse (BeefyResponse) dstService(256) with messageId='{}', vpi='{}'", beefy.getId(), moMessage.getVehiclePlatformId());
        tceMock.sendMoJmsMessage(moMessage);
      }
    }
  }

  private void tceReplyPongV1(MtMessage mtMessage) {
    DecodedV1 decoded = decodeV1(mtMessage);

    if (decoded.getPing() != null) {
      Ping ping = decoded.getPing();

      if (ping.getResponseExpected()) {
        byte[] encodedPong = pingCoder.encodePong(ping.getId());
        tceMock.sendMoMessage(mtMessage.getVehiclePlatformId(), encodedPong, 1);
      }
    }
  }

  private void tceReplyPongV2(MtMessage mtMessage, boolean validChecksum) {
    TestService decoded = decodeV2(mtMessage);

    if (decoded.getPingRequest() != null) {
      PingRequest ping = decoded.getPingRequest();

      if (!validChecksum) {
        ping.getPayload().setChecksum(ChecksumCalculator.getSHA256Hash("RanomPayload"));
      }

      MoDtoV2 dto = createMoDtoV2(mtMessage, decoded);
      MoMessage moMessage = TestUtil.createMoMessage(pingCoderV2.encodePong(dto, null), 2, mtMessage.getVehiclePlatformId());
      logger.info("Sending TCE-MoResponse (Pong) dstService(256.2) with correlationID='{}', vpi='{}'", ping.getCorrelationId(), dto.getVpi());

      tceMock.sendMoJmsMessage(moMessage);
    }
  }
}
