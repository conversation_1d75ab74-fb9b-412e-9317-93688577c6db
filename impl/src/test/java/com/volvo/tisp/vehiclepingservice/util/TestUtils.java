package com.volvo.tisp.vehiclepingservice.util;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.Action;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

public class TestUtils {
  public static final String CORRELATION_ID = "test-correlation-id";
  public static final long MESSAGE_ID = 1L;
  public static final byte[] PAYLOAD = "test-payload".getBytes();
  public static final Vpi VPI = Vpi.ofString("12345678901234567890ABCDEFAAAAAA");

  private TestUtils() {
  }

  public static void assertThrowIllegalArgumentException(Action action, String expectedMessage) {
    AssertThrows.illegalArgumentException(action, expectedMessage);
  }
}
