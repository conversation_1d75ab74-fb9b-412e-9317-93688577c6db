package com.volvo.tisp.vehiclepingservice.jms;

import java.util.Base64;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.subscriptionrepository.client.DefaultMessagePublisher;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.mt.message.client.json.v1.MessageTypes;
import com.volvo.tisp.vc.mt.message.client.json.v1.MtMessage;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.volvo.tisp.vehiclepingservice.util.ServiceAccessTokenService;
import com.volvo.tisp.vehiclepingservice.metrics.MtDispatcherMetricReporter;

public class MtDispatcherMessagePublisherTest {
  private MessagePublisher.Message<MtDto> mockMessage;
  private MtDispatcherMessagePublisher mtDispatcherMessagePublisher;
  private ServiceAccessTokenService serviceAccessTokenService;
  private MtDispatcherMetricReporter mtDispatcherMetricReporter;

  private static MtDto createMtDto() {
    MtDto dto = new MtDto();
    dto.setVpi(Vpi.ofString("1234567890ABCDEF1234567890ABCDEF"));
    dto.setServiceVersion(1);
    dto.setCorrelationId("testCorrelationId");
    dto.setPayload("testPayload".getBytes());
    dto.setServiceAccessToken("testToken");
    dto.setChannel(Channel.UDP);
    dto.setTimeout(300);
    dto.setSystem("testSystem");
    return dto;
  }

  private static void verifyMtMessage(MtMessage result) {
    Assertions.assertEquals("1234567890ABCDEF1234567890ABCDEF", result.getVehiclePlatformId());
    Assertions.assertEquals(1, result.getServiceVersion());
    Assertions.assertEquals("testCorrelationId", result.getCorrelationId());
    Assertions.assertEquals(Base64.getEncoder().encodeToString("testPayload".getBytes()), result.getPayload());
    Assertions.assertEquals("testToken", result.getServiceAccessToken());
    Assertions.assertEquals("ping-udp-only", result.getServiceFunction());
  }

  @Test
  void convertCreatesCorrectMtMessageTest() {
    MtDto dto = createMtDto();

    TispContext.runInContext(() -> {
      MtMessage mtMessage = mtDispatcherMessagePublisher.convert(dto);
      verifyMtMessage(mtMessage);
    });
  }

  @Test
  void publishCreatesCorrectMtMessageExceptionTest() {
    MtDto dto = createMtDto();

    Mockito.when(serviceAccessTokenService.getServiceAccessToken()).thenReturn(CompletableFuture.completedFuture("testToken"));

    Mockito.when(mockMessage.correlationId(Mockito.anyString())).thenReturn(mockMessage);
    Mockito.when(mockMessage.option(Mockito.anyString(), Mockito.anyString())).thenReturn(mockMessage);
    Mockito.when(mockMessage.replyTo(Mockito.anyString())).thenReturn(mockMessage);
    Mockito.when(mockMessage.publish(Mockito.any(MtDto.class))).thenReturn(CompletableFuture.completedFuture(0));

    TispContext.runInContext(() -> Assertions.assertThrows(CompletionException.class, () -> mtDispatcherMessagePublisher.publish(dto).join()));
  }

  @Test
  void publishCreatesCorrectMtMessageNoSubscriberExceptionTest() {
    MtDto dto = createMtDto();

    Mockito.when(serviceAccessTokenService.getServiceAccessToken()).thenReturn(CompletableFuture.completedFuture("testToken"));

    Mockito.when(mockMessage.correlationId(Mockito.anyString())).thenReturn(mockMessage);
    Mockito.when(mockMessage.option(Mockito.anyString(), Mockito.anyString())).thenReturn(mockMessage);
    Mockito.when(mockMessage.replyTo(Mockito.anyString())).thenReturn(mockMessage);
    Mockito.when(mockMessage.publish(Mockito.any(MtDto.class))).thenReturn(CompletableFuture.completedFuture(0));

    TispContext.runInContext(() -> {
      CompletionException thrown = Assertions.assertThrows(
          CompletionException.class,
          () -> mtDispatcherMessagePublisher.publish(dto).join()
      );

      Throwable originalException = thrown.getCause();
      Assertions.assertEquals(
          "java.util.concurrent.CompletionException: com.volvo.tisp.vehiclepingservice.domain.exceptions.NoSubscriberException: No subscribers for MtMessage 1.0 for Ping (Mt Dispatcher)",
          originalException.getMessage());
    });

  }

  @Test
  void publishCreatesCorrectMtMessagePublishingSuccessTest() {
    MtDto dto = createMtDto();

    Mockito.when(serviceAccessTokenService.getServiceAccessToken()).thenReturn(CompletableFuture.completedFuture("testToken"));

    Mockito.when(mockMessage.correlationId(Mockito.anyString())).thenReturn(mockMessage);
    Mockito.when(mockMessage.option(Mockito.anyString(), Mockito.anyString())).thenReturn(mockMessage);
    Mockito.when(mockMessage.replyTo(Mockito.anyString())).thenReturn(mockMessage);
    Mockito.when(mockMessage.publish(Mockito.any(MtDto.class))).thenReturn(CompletableFuture.completedFuture(1));

    TispContext.runInContext(() -> Assertions.assertAll(() -> mtDispatcherMessagePublisher.publish(dto).join()));
  }

  @BeforeEach
  void setUp() {
    mockMessage = Mockito.mock(MessagePublisher.Message.class);
    DefaultMessagePublisher.Builder messagePublisherBuilder = getDefaultMessagePublisherBuilder();

    serviceAccessTokenService = Mockito.mock(ServiceAccessTokenService.class);
    mtDispatcherMetricReporter = Mockito.mock(MtDispatcherMetricReporter.class);
    mtDispatcherMessagePublisher = new MtDispatcherMessagePublisher(messagePublisherBuilder, serviceAccessTokenService,
        new ServiceFunctionCalculator(), mtDispatcherMetricReporter);
  }

  private DefaultMessagePublisher.Builder getDefaultMessagePublisherBuilder() {
    MessagePublisher<MtDto> messagePublisher = Mockito.mock(MessagePublisher.class);

    DefaultMessagePublisher.Builder messagePublisherBuilder = Mockito.mock(DefaultMessagePublisher.Builder.class);

    MessagePublisher.Builder.Step2<MtDto> messagePublisherBuilderStep2 = Mockito.mock(DefaultMessagePublisher.Builder.Step2.class);
    MessagePublisher.Builder.Step3<MtDto> messagePublisherBuilderStep3 = Mockito.mock(DefaultMessagePublisher.Builder.Step3.class);

    Mockito.when(messagePublisherBuilder.messageType(MessageTypes.MT_MESSAGE, MtDto.class)).thenReturn(messagePublisherBuilderStep2);
    Mockito.when(messagePublisherBuilderStep2.version(Mockito.eq(MessageTypes.VERSION_1_0), Mockito.any()))
        .thenReturn(messagePublisherBuilderStep3);
    Mockito.when(messagePublisherBuilderStep3.build()).thenReturn(messagePublisher);

    Mockito.when(messagePublisher.newMessage()).thenReturn(mockMessage);
    return messagePublisherBuilder;
  }
}

