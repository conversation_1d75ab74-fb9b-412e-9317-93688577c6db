# We always listen on the same port, regardless of "env"
vps.server.port=12590
#logging
vps.logging.integration.server-out.enabled=true
vps.logging.integration.client-out.enabled=true
vps.logging.integration.server-in.enabled=true
vps.logging.integration.client-out.payload.enabled=true
vps.logging.integration.server-out.payload.enabled=true
vps.logging.integration.server-in.payload.enabled=true
vps.logging.integration.client-in.payload.enabled=true


# common mongodb settings
vps.mongodb.authdb=admin
vps.mongodb.port=27017
vps.mongodb.password=udis14${zone}
vps.shared.mongodb.host=mongodb-a.mongodb,mongodb-b.mongodb,mongodb-c.mongodb


## test
vps.shared.test.mongodb.host=mongodb-a.singlenode-mongodb,mongodb-b.singlenode-mongodb,mongodb-c.singlenode-mongodb
vps.shared.test.mongodb.password=udis14qa

#How many seconds for timeout between ping and pong
vps.timeout.seconds=11
vps.shared.prod.us-east-1.timeout.seconds=30
vps.shared.qa.us-east-1.timeout.seconds=30
vps.shared.iot1.us-east-1.timeout.seconds=30

vps.shared.prod.eu-west-1.timeout.seconds=15
vps.shared.qa.eu-west-1.timeout.seconds=15
vps.shared.iot1.eu-west-1.timeout.seconds=15

## pool
platform.de.servicediscovery.auth=http://mockhost:8080/
platform.de.servicediscovery.subr=http://mockhost:8080/
platform.de.servicediscovery.state=http://testhost:10280
platform.de.servicediscovery.tus=http://mockhost:8080/
platform.de.servicediscovery.vil=http://mockhost:8080/

platform.de.spring.artemis.broker-url=tcp://mockhost:61616
platform.de.spring.artemis.user=admin
platform.de.spring.artemis.password=admin

vps.de.mongodb.host=mockhost
vps.de.mongodb.authdb=
vps.de.mongodb.password=bla


## local
platform.local.management.tracing.enabled=false
platform.local.management.influx.metrics.export.enabled=false

vps.local.mongodb.host=localhost
vps.local.mongodb.authdb=
vps.local.mongodb.password=bla

platform.local.servicediscovery.auth=http://localhost:8080/
platform.local.servicediscovery.subr=http://localhost:8080/
platform.local.servicediscovery.state=http://localhost:10280/
platform.local.servicediscovery.tus=http://localhost:8080/
platform.local.servicediscovery.vil=http://localhost:8080/

platform.local.spring.artemis.broker-url=tcp://localhost:61616
platform.local.spring.artemis.user=admin
platform.local.spring.artemis.password=admin

vps.local.web.additionalResourceLocationRoots=file:gui/src/main/resources/
