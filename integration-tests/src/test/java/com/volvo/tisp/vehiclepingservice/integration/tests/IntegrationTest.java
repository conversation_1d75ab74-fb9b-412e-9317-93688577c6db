package com.volvo.tisp.vehiclepingservice.integration.tests;

import java.io.IOException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.testcontainers.context.ImportTestcontainers;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.junit.jupiter.Testcontainers;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.nimbusds.jose.JOSEException;
import com.volvo.tisp.framework.test.autoconfigure.jms.artemis.AutoConfigureArtemis;
import com.volvo.tisp.vehiclepingservice.conf.AppConfig;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.integration.tests.conf.Containers;
import com.volvo.tisp.vehiclepingservice.integration.tests.conf.IntegrationTestConfig;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.MtDispatcherMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.mock.TceMock;
import com.volvo.tisp.vehiclepingservice.integration.tests.v1.PingV1NoPayloadLegacy;
import com.volvo.tisp.vehiclepingservice.integration.tests.v1.PingV2NoPayloadBadObsTokenLegacy;
import com.volvo.tisp.vehiclepingservice.integration.tests.v1.PingV2NoPayloadLegacy;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.BadRequestBig;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.BadRequestCopy;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.BadRequestTus;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.BadRequestVpi;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.BeefyFromVehicle;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.BeefyV1;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV1NoPayload;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV1NoPayloadFromVehicle;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV2NoCopyPayload;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV2NoCopyPayloadSat;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV2NoPayload;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV2NoPayloadBadObsToken;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV2NoPayloadFromVehicle;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV2NoPayloadMtTerminatingMtStatuses;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV2NoPayloadTempMtPublisherSplit;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV2WithPayload;
import com.volvo.tisp.vehiclepingservice.integration.tests.v2.PingV2withPayloadBadChecksum;

/**
 * Basic integration test for Vehicle Ping Service Application.
 */
@ImportTestcontainers(Containers.class)
@SpringBootTest(classes = {AppConfig.class, IntegrationTestConfig.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@AutoConfigureArtemis
@Testcontainers
public class IntegrationTest {
  private final BadRequestBig badRequestBig;
  private final BadRequestCopy badRequestCopy;
  private final BadRequestTus badRequestTus;
  private final BadRequestVpi badRequestVpi;
  private final BeefyFromVehicle beefyFromVehicle;
  private final BeefyV1 beefyV1;
  private final GetDataOk getDataOk;
  private final HealthCheckTest healthCheckTest;
  private final MtDispatcherMock mtDispatcherMock;
  private final PingEntityService pingEntityService;
  private final PingV1NoPayload pingV1NoPayload;
  private final PingV1NoPayloadFromVehicle pingV1NoPayloadFromVehicle;
  private final PingV1NoPayloadLegacy pingV1NoPayloadLegacy;
  private final PingV2NoCopyPayload pingV2NoCopyPayload;
  private final PingV2NoCopyPayloadSat pingV2NoCopyPayloadSat;
  private final PingV2NoPayload pingV2NoPayload;
  private final PingV2NoPayloadBadObsToken pingV2NoPayloadBadObsToken;
  private final PingV2NoPayloadBadObsTokenLegacy pingV2NoPayloadBadObsTokenLegacy;
  private final PingV2NoPayloadFromVehicle pingV2NoPayloadFromVehicle;
  private final PingV2NoPayloadLegacy pingV2NoPayloadLegacy;
  private final PingV2NoPayloadMtTerminatingMtStatuses pingV2NoPayloadMtTerminatingMtStatuses;
  private final PingV2NoPayloadTempMtPublisherSplit pingV2NoPayloadTempMtPublisherSplit;
  private final PingV2WithPayload pingV2WithPayload;
  private final PingV2withPayloadBadChecksum pingV2withPayloadBadChecksum;
  private final TceMock tceMock;

  @Autowired
  public IntegrationTest(BadRequestBig badRequestBig, BadRequestCopy badRequestCopy, BadRequestTus badRequestTus, BadRequestVpi badRequestVpi,
      BeefyFromVehicle beefyFromVehicle, BeefyV1 beefyV1, GetDataOk getDataOk, HealthCheckTest healthCheckTest, MtDispatcherMock mtDispatcherMock,
      PingEntityService pingEntityService, PingV1NoPayload pingV1NoPayload, PingV1NoPayloadFromVehicle pingV1NoPayloadFromVehicle,
      PingV1NoPayloadLegacy pingV1NoPayloadLegacy, PingV2NoCopyPayload pingV2NoCopyPayload, PingV2NoCopyPayloadSat pingV2NoCopyPayloadSat,
      PingV2NoPayload pingV2NoPayload, PingV2NoPayloadBadObsToken pingV2NoPayloadBadObsToken, PingV2NoPayloadBadObsTokenLegacy pingV2NoPayloadBadObsTokenLegacy,
      PingV2NoPayloadFromVehicle pingV2NoPayloadFromVehicle, PingV2NoPayloadLegacy pingV2NoPayloadLegacy,
      PingV2NoPayloadMtTerminatingMtStatuses pingV2NoPayloadMtTerminatingMtStatuses, PingV2NoPayloadTempMtPublisherSplit pingV2NoPayloadTempMtPublisherSplit,
      PingV2WithPayload pingV2WithPayload, PingV2withPayloadBadChecksum pingV2withPayloadBadChecksum, TceMock tceMock) {
    this.badRequestBig = badRequestBig;
    this.badRequestCopy = badRequestCopy;
    this.badRequestTus = badRequestTus;
    this.badRequestVpi = badRequestVpi;
    this.beefyFromVehicle = beefyFromVehicle;
    this.beefyV1 = beefyV1;
    this.getDataOk = getDataOk;
    this.healthCheckTest = healthCheckTest;
    this.mtDispatcherMock = mtDispatcherMock;
    this.pingEntityService = pingEntityService;
    this.pingV1NoPayload = pingV1NoPayload;
    this.pingV1NoPayloadFromVehicle = pingV1NoPayloadFromVehicle;
    this.pingV1NoPayloadLegacy = pingV1NoPayloadLegacy;
    this.pingV2NoCopyPayload = pingV2NoCopyPayload;
    this.pingV2NoCopyPayloadSat = pingV2NoCopyPayloadSat;
    this.pingV2NoPayload = pingV2NoPayload;
    this.pingV2NoPayloadBadObsToken = pingV2NoPayloadBadObsToken;
    this.pingV2NoPayloadBadObsTokenLegacy = pingV2NoPayloadBadObsTokenLegacy;
    this.pingV2NoPayloadFromVehicle = pingV2NoPayloadFromVehicle;
    this.pingV2NoPayloadLegacy = pingV2NoPayloadLegacy;
    this.pingV2NoPayloadMtTerminatingMtStatuses = pingV2NoPayloadMtTerminatingMtStatuses;
    this.pingV2NoPayloadTempMtPublisherSplit = pingV2NoPayloadTempMtPublisherSplit;
    this.pingV2WithPayload = pingV2WithPayload;
    this.pingV2withPayloadBadChecksum = pingV2withPayloadBadChecksum;
    this.tceMock = tceMock;
  }

  @DynamicPropertySource
  static void configureProperties(DynamicPropertyRegistry registry) {
    registry.add("servicediscovery.auth", Containers.wireMockContainer::getBaseUrl);
    registry.add("servicediscovery.subr", Containers.wireMockContainer::getBaseUrl);
    registry.add("servicediscovery.state", Containers.wireMockContainer::getBaseUrl);
    registry.add("servicediscovery.tus", Containers.wireMockContainer::getBaseUrl);
    registry.add("servicediscovery.vil", Containers.wireMockContainer::getBaseUrl);
    registry.add("servicediscovery.idpm2m", Containers.wireMockContainer::getBaseUrl);
    registry.add("s3Values.mockUrl", () -> Containers.localStackContainer.getEndpoint().toString());
  }

  @ParameterizedTest
  @CsvSource({
      "true, true, true, false",
      "true, true, true, true",
      "true, false, true, false",
      "false, false, true, false",
      "false, false, false, false"
  })
  void beefyFromVehicle(
      boolean mtDispatcher, boolean useMoRouter, boolean copyPayload, boolean tokenValidation) throws Exception {
    beefyFromVehicle.test(mtDispatcher, useMoRouter, copyPayload, tokenValidation);
  }

  @ParameterizedTest
  @CsvSource({"true, true, false", "true, true, true", "true, false, false", "false, false, false"})
  void beefyV1(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    beefyV1.test(mtDispatcher, useMoRouter, tokenValidation);
  }

  @BeforeEach
  void beforeEach() {
    WireMock.resetAllRequests();
    pingEntityService.removeAll();
    tceMock.clear();
    mtDispatcherMock.clear();
  }

  @Test
  void getDataOk() throws JOSEException, IOException {
    getDataOk.test();
  }

  @Test
  void healthCheckTest() {
    healthCheckTest.test();
  }

  @ParameterizedTest
  @CsvSource({"true, true, false", "true, true, true", "true, false, false", "false, false, false"})
  void pingV1NoPayload(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    pingV1NoPayload.test(mtDispatcher, useMoRouter, tokenValidation);
  }

  @ParameterizedTest
  @CsvSource({"true, true, false", "true, true, true", "true, false, false", "false, false, false"})
  void pingV1NoPayloadFromVehicle(
      boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    pingV1NoPayloadFromVehicle.test(mtDispatcher, useMoRouter, tokenValidation);
  }

  @ParameterizedTest
  @CsvSource({
      "true, true, false, false",
      "true, true, true, false",
      "true, false, false, false",
      "false, false, false, false",
      "false, false, false, true",
      "true, true, true, true"
  })
  void pingV1NoPayloadLegacy(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation, boolean unauthorized) throws Exception {
    pingV1NoPayloadLegacy.test(mtDispatcher, useMoRouter, tokenValidation, unauthorized);
  }

  @ParameterizedTest
  @CsvSource({"true, true, false", "true, true, true", "true, false, false"})
  void pingV2NoCopyPayload(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    pingV2NoCopyPayload.test(mtDispatcher, useMoRouter, tokenValidation);
  }

  @ParameterizedTest
  @CsvSource({"true, true, false", "true, true, true", "true, false, false"})
  void pingV2NoCopyPayloadSat(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    pingV2NoCopyPayloadSat.test(mtDispatcher, useMoRouter, tokenValidation);
  }

  @ParameterizedTest
  @CsvSource({"true, true, false", "true, true, true", "true, false, true"})
  void pingV2NoPayload(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    pingV2NoPayload.test(mtDispatcher, useMoRouter, tokenValidation);
  }

  @ParameterizedTest
  @ValueSource(ints = {1, 2, 3})
  void pingV2NoPayloadBadObsToken(int badType) throws Exception {
    pingV2NoPayloadBadObsToken.test(badType);
  }

  @ParameterizedTest
  @ValueSource(ints = {1, 2, 3})
  void pingV2NoPayloadBadObsTokenLegacy(int badType) throws Exception {
    pingV2NoPayloadBadObsTokenLegacy.test(badType);
  }

  @ParameterizedTest
  @CsvSource({"true, true, false", "true, true, true", "true, false, false"})
  void pingV2NoPayloadFromVehicle(
      boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    pingV2NoPayloadFromVehicle.test(mtDispatcher, useMoRouter, tokenValidation);
  }

  @ParameterizedTest
  @CsvSource({
      "true, true, false, false",
      "true, true, true, false",
      "true, false, true, false",
      "true, true, true, true"
  })
  void pingV2NoPayloadLegacy(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation, boolean unauthorized) throws Exception {
    pingV2NoPayloadLegacy.test(mtDispatcher, useMoRouter, tokenValidation, unauthorized);
  }

  @ParameterizedTest
  @CsvSource({
      "true, 1",
      "true, 2",
      "true, 3",
      "true, 4",
      "true, 5",
      "false, 1",
      "false, 2",
      "false, 3",
      "false, 4",
      "false, 5"
  })
  void pingV2NoPayloadMtTerminatingMtStatuses(boolean mtDispatcher, int mtStatusType) throws Exception {
    pingV2NoPayloadMtTerminatingMtStatuses.test(mtDispatcher, mtStatusType);
  }

  @ParameterizedTest
  @CsvSource({"true", "false"})
  void pingV2NoPayloadTempMtPublisherSplit(boolean tokenValidation) throws Exception {
    pingV2NoPayloadTempMtPublisherSplit.test(tokenValidation);
  }

  @ParameterizedTest
  @CsvSource({"true, true, false", "true, true, true", "true, false, false"})
  void pingV2WithPayload(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    pingV2WithPayload.test(mtDispatcher, useMoRouter, tokenValidation);
  }

  @ParameterizedTest
  @CsvSource({"true, true, false", "true, true, true", "true, false, false"})
  void pingV2withPayloadBadChecksum(boolean mtDispatcher, boolean useMoRouter, boolean tokenValidation) throws Exception {
    pingV2withPayloadBadChecksum.test(mtDispatcher, useMoRouter, tokenValidation);
  }

  @Test
  void testBadRequestBig() throws Exception {
    badRequestBig.test();
  }

  @Test
  void testBadRequestCopy() throws Exception {
    badRequestCopy.test();
  }

  @Test
  void testBadRequestTus() throws Exception {
    badRequestTus.test();
  }

  @Test
  void testBadRequestVpi() throws Exception {
    badRequestVpi.test();
  }
}