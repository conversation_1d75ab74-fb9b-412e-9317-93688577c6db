package com.volvo.tisp.vehiclepingservice.swap.common;

import java.io.IOException;
import java.io.ObjectInput;
import java.io.ObjectOutput;
import java.io.UnsupportedEncodingException;

import org.jdom2.Element;

public abstract class AbstractStringType extends ASNConstrainedValue {
  private static final long serialVersionUID = 1L;
  protected String value = null;

  protected AbstractStringType(String name) {
    this.constrainType = 4;
    this.setAsnObjectName(name);
  }

  public abstract ASNValue createObject() throws ASNException;

  public abstract void decode(PERStream var1) throws ASNException;

  public void decode(Element oElement) throws ASNException {
    this.set(oElement.getTextTrim());
  }

  public abstract void encode(PERStream var1) throws ASNException;

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(this.getAsnObjectName());
    oElem.addContent(this.get());
    oElement.addContent(oElem);
  }

  public abstract long encodedSize();

  public String get() {
    return this.value;
  }

  public byte[] getAsByteArray(String charsetName) throws UnsupportedEncodingException {
    return this.value.getBytes(charsetName);
  }

  public long getSize() {
    return (long) this.value.length();
  }

  public void readExternal(ObjectInput in) throws IOException, ClassNotFoundException {
    super.readExternal(in);
    this.value = in.readUTF();
  }

  public void set(String val) {
    this.value = val;
  }

  public void writeExternal(ObjectOutput out) throws IOException {
    super.writeExternal(out);
    out.writeUTF(this.value);
  }
}
