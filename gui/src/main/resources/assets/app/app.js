"use strict";

angular.module("app", ["ui.router","app.info", "app.home"])

  .run(function ($rootScope, $state, $stateParams, $location) {
    $rootScope.$state = $state;
    $rootScope.$stateParams = $stateParams;
     var path = function() { return $location.path();};
       $rootScope.$watch(path, function(newVal, oldVal){
         $rootScope.activetab = newVal;
       });

  })

  .config(function($urlRouterProvider) {
    $urlRouterProvider.otherwise("/home");
  });
