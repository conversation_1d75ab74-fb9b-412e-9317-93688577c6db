package com.volvo.tisp.vehiclepingservice.domain.v1;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;
import com.volvo.tisp.vehiclepingservice.util.TimeoutCalculator;
import com.wirelesscar.telematicunitservice.TelematicUnitService;

@Component
public class ReceiveMoPingSendMtPongStepV1 extends ReceiveMoStep {

  private final PingCoder pingCoder;

  public ReceiveMoPingSendMtPongStepV1(MtMessagePublisher mtMessagePublisher, PingCoder pingCoder, TimeoutCalculator timeoutCalculator, TelematicUnitService tusClient) {
    super(mtMessagePublisher, tusClient, timeoutCalculator);
    this.pingCoder = pingCoder;
  }

  @Override
  protected byte[] getPayload(MoDtoV1 moDtoV1) {
    return pingCoder.encodePong(moDtoV1.getDecoded().getPing().getId());
  }
}
