package com.volvo.tisp.vehiclepingservice.domain.v1;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.jms.MtMessagePublisher;

@ExtendWith(MockitoExtension.class)
class SendMtPingStepV1Test {
  @Mock
  private MtMessagePublisher mtMessagePublisher;
  @Mock
  private PingCoder pingCoder;
  @InjectMocks
  private SendMtPingStepV1 sendMtPingStepV1;
  @Mock
  private StateRepository stateClient;

  @Test
  void getPayloadShouldEncodePing() {
    long swapCorrelationId = 12345L;

    byte[] expectedEncodedPayload = "encoded payload".getBytes();
    Mockito.when(pingCoder.encodePing(swapCorrelationId)).thenReturn(expectedEncodedPayload);

    byte[] result = sendMtPingStepV1.getPayload(null, swapCorrelationId);

    Mockito.verify(pingCoder).encodePing(swapCorrelationId);
    Assertions.assertArrayEquals(expectedEncodedPayload, result);
  }

  @Test
  void getPayloadShouldHandleEncodingException() {
    long swapCorrelationId = 12345L;

    Mockito.when(pingCoder.encodePing(swapCorrelationId)).thenThrow(new RuntimeException("Encoding failed"));

    Assertions.assertThrows(RuntimeException.class, () -> sendMtPingStepV1.getPayload(null, swapCorrelationId));
    Mockito.verify(pingCoder).encodePing(swapCorrelationId);
  }

}
