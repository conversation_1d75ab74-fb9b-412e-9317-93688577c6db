package com.volvo.tisp.vehiclepingservice.domain;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Locale;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ResponseStatusException;

import com.volvo.tisp.framework.logging.Retention;
import com.volvo.tisp.staterepository.StateRepository;
import com.volvo.tisp.vehiclepingservice.database.entity.PingEntity;
import com.volvo.tisp.vehiclepingservice.database.entity.Status;
import com.volvo.tisp.vehiclepingservice.database.repository.MessageIdRepo;
import com.volvo.tisp.vehiclepingservice.domain.db.PingEntityService;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.BadRequestException;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.InternalServerErrorException;
import com.volvo.tisp.vehiclepingservice.domain.exceptions.NotFoundException;
import com.volvo.tisp.vehiclepingservice.domain.v1.ReceiveBeefyResponseStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.ReceiveMoBeefySendMtBeefyResponseStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.ReceiveMoPingSendMtPongStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.ReceivePongStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.SendMtBeefyStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.SendMtPingStepV1;
import com.volvo.tisp.vehiclepingservice.domain.v1.dto.MoDtoV1;
import com.volvo.tisp.vehiclepingservice.domain.v2.ReceiveMoPingSendMtPongStepV2;
import com.volvo.tisp.vehiclepingservice.domain.v2.ReceivePongStepV2;
import com.volvo.tisp.vehiclepingservice.domain.v2.SendMtPingStepV2;
import com.volvo.tisp.vehiclepingservice.domain.v2.dto.MoDtoV2;
import com.volvo.tisp.vehiclepingservice.metrics.PingManagerMetricReporter;
import com.volvo.tisp.vehiclepingservice.rest.v2.RequestData;
import com.wirelesscar.telematicunitservice.TelematicUnitService;

@Component
public class PingManager {
  public static final int SWAP_SERVICE_ID = 256;
  private static final Logger logger = LoggerFactory.getLogger(PingManager.class);

  private final MessageIdRepo messageIdRepo;
  private final PingEntityService pingEntityService;
  private final PingManagerMetricReporter pingManagerMetricReporter;
  private final ReceiveBeefyResponseStepV1 receiveBeefyResponseStepV1;
  private final ReceiveMoBeefySendMtBeefyResponseStepV1 receiveMoBeefySendMtBeefyResponseStepV1;
  private final ReceiveMoPingSendMtPongStepV1 receiveMoPingSendMtPongStepV1;
  private final ReceiveMoPingSendMtPongStepV2 receiveMoPingSendMtPongStepV2;
  private final ReceivePongStepV1 receivePongStepV1;
  private final ReceivePongStepV2 receivePongStepV2;
  private final SendMtBeefyStepV1 sendMtBeefyStepV1;
  private final SendMtPingStepV1 sendMtPingStepV1;
  private final SendMtPingStepV2 sendMtPingStepV2;
  private final StateRepository stateRepository;
  private final TelematicUnitService telematicUnitService;

  public PingManager(MessageIdRepo messageIdRepo, PingEntityService pingEntityService,
      PingManagerMetricReporter pingManagerMetricReporter, ReceiveBeefyResponseStepV1 receiveBeefyResponseStepV1,
      ReceiveMoBeefySendMtBeefyResponseStepV1 receiveMoBeefySendMtBeefyResponseStepV1,
      ReceiveMoPingSendMtPongStepV1 receiveMoPingSendMtPongStepV1, ReceiveMoPingSendMtPongStepV2 receiveMoPingSendMtPongStepV2,
      ReceivePongStepV1 receivePongStepV1, ReceivePongStepV2 receivePongStepV2, SendMtBeefyStepV1 sendMtBeefyStepV1, SendMtPingStepV2 sendMtPingStepV2,
      SendMtPingStepV1 sendMtPingStepV1, StateRepository stateRepository, TelematicUnitService telematicUnitService) {
    this.messageIdRepo = messageIdRepo;
    this.pingEntityService = pingEntityService;
    this.pingManagerMetricReporter = pingManagerMetricReporter;
    this.receiveBeefyResponseStepV1 = receiveBeefyResponseStepV1;
    this.receiveMoBeefySendMtBeefyResponseStepV1 = receiveMoBeefySendMtBeefyResponseStepV1;
    this.receiveMoPingSendMtPongStepV1 = receiveMoPingSendMtPongStepV1;
    this.receiveMoPingSendMtPongStepV2 = receiveMoPingSendMtPongStepV2;
    this.receivePongStepV1 = receivePongStepV1;
    this.receivePongStepV2 = receivePongStepV2;
    this.sendMtBeefyStepV1 = sendMtBeefyStepV1;
    this.sendMtPingStepV2 = sendMtPingStepV2;
    this.sendMtPingStepV1 = sendMtPingStepV1;
    this.stateRepository = stateRepository;
    this.telematicUnitService = telematicUnitService;
  }

  public CompletableFuture<PingEntity> getData(String correlationIdString) {
    return pingEntityService.findByCorrelationId(correlationIdString);
  }

  public CompletableFuture<String> pingRest(RequestData requestData) {
    return telematicUnitService.getVehicleServiceVersion(requestData.vpi(), Integer.toString(SWAP_SERVICE_ID))
        .exceptionally(throwable -> {
          if (throwable instanceof BadRequestException e) {
            throw e;
          } else if (throwable.getCause() instanceof BadRequestException e) {
            throw e;
          } else if (throwable instanceof WebClientResponseException.NotFound e) {
            throw new NotFoundException("TUS gave 404 for VPI: " + requestData.vpi(), e);
          }
          throw new RuntimeException(throwable);
        })
        .thenCompose(telematicUnit -> {
          if (telematicUnit.getVersion() == 2) {
            return pingV2(requestData, telematicUnit.getSystem());
          } else if (telematicUnit.getVersion() == 1) {
            return pingV1(requestData, telematicUnit.getSystem());
          }
          throw new ResponseStatusException(HttpStatusCode.valueOf(400), "Vehicle has an unsupported version: " + telematicUnit.getVersion());
        });
  }

  public CompletableFuture<Void> processMoMessageV1(MoDtoV1 dto) {
    if (dto.getDecoded().getPing() != null) {
      // Ping from Vehicle
      pingManagerMetricReporter.onPingFromVehicleV1();
      return receiveMoPingSendMtPongStepV1.action(dto);
    } else if (dto.getDecoded().getBeefyMessage() != null) {
      // Beefy from Vehicle
      pingManagerMetricReporter.onBeefyFromVehicle();
      return receiveMoBeefySendMtBeefyResponseStepV1.action(dto);
    } else if (dto.getDecoded().getPong() != null) {
      // Pong from Vehicle
      pingManagerMetricReporter.onPongFromVehicleV1();
      return receivePongStepV1.action(dto.getDecoded().getPong().getId(), dto.getVpi());
    } else if (dto.getDecoded().getBeefyMessageResponse() != null) {
      // BeefyResponse from Vehicle
      pingManagerMetricReporter.onBeefyResponseFromVehicle();
      return receiveBeefyResponseStepV1.action(dto);
    }

    String err = String.format(Locale.ROOT, "Received MO V1 with no request or response, vpi: %s, correlationId: %s", dto.getVpi(), dto.getCorrelationId());
    logger.error(err);
    throw new BadRequestException(err);
  }

  public CompletableFuture<Void> processMoMessageV2(MoDtoV2 dto) {
    if (dto.getDecoded().getPingRequest() != null) {
      pingManagerMetricReporter.onPingFromVehicleV2();
      return receiveMoPingSendMtPongStepV2.action(dto);
    } else if (dto.getDecoded().getPingResponse() != null) {
      pingManagerMetricReporter.onPongFromVehicleV2();
      return receivePongStepV2.action(dto);
    }

    // TODO more info on error!
    throw new InternalServerErrorException("Received MO V2 with no request or response");
  }

  /**
   * Process MtStatus and possible terminating action depending on mt-status and entity-status
   *
   * @param correlationId correlation id
   * @param status        MtMessage status
   * @return CompletableFuture with no result
   */
  public CompletableFuture<Void> processMtStatus(String correlationId, com.volvo.tisp.vc.mt.message.client.json.v1.Status status) {
    return pingEntityService.findByCorrelationId(correlationId)
        .thenCompose(pingEntity -> {
          if (pingEntity != null) {
            logger.info("Received MtStatus with status: {} for Vpi: {} and CorrelationId: {}", status, pingEntity.getVpi(), correlationId);

            return handleMtStatus(pingEntity, status)
                .thenCompose(pingEntityService::upsert)
                .thenApply(v -> null);
          }

          // if we receive something we don't have in DB, like ping from Vehicle
          return CompletableFuture.completedFuture(null);
        });
  }

  private CompletableFuture<Void> createAndSaveEntity(String vpi, String correlationId, int serviceVersion, byte[] payload, Boolean copyPayload,
      Long swapCorrelationId) {
    logger.debug("Creating DB entity for V{}, Vpi: {}, correlationId: {}, swapCorrelationId: {}", serviceVersion, vpi, correlationId, swapCorrelationId);

    PingEntity entity = new PingEntity();
    entity.setVpi(vpi);
    entity.setCorrelationId(correlationId);
    entity.setStatus(Status.PENDING);
    entity.setStartTimeInMillis(Instant.now().toEpochMilli());
    entity.setServiceVersion(serviceVersion);
    entity.setPayloadSent(payload);
    entity.setMessageId(swapCorrelationId);
    entity.setCopyPayload(copyPayload);

    return pingEntityService.upsert(entity)
        .thenAccept(pingEntity -> logger.debug(Retention.SHORT, "Entity saved to DB for vpi: {}, correlationId: {}, serviceVersion: {}", vpi, correlationId,
            serviceVersion))
        .toCompletableFuture();
  }

  private CompletableFuture<Void> createAndSaveEntity(RequestData requestData, UUID correlationId, Long swapCorrelationId, int serviceVersion) {
    byte[] payload = requestData.body() == null ? null : requestData.body().getBytes(StandardCharsets.UTF_8);

    return createAndSaveEntity(requestData.vpi(), correlationId.toString(), serviceVersion, payload, requestData.copyPayload(), swapCorrelationId);
  }

  private CompletableFuture<PingEntity> handleMtStatus(PingEntity pingEntity, com.volvo.tisp.vc.mt.message.client.json.v1.Status mtStatus) {
    Status status = switch (mtStatus) {
      case TIMEOUT -> Status.TIMEOUT;
      case DELIVERED -> Status.PENDING;
      case OVERRIDDEN -> Status.OVERRIDDEN;
      case CANCELED -> Status.CANCELLED;
      default -> Status.ERROR;
    };

    pingEntity.setMtStatus(mtStatus.value());

    // For all non delivered cases
    if (pingEntity.getStatus() == Status.PENDING && mtStatus != com.volvo.tisp.vc.mt.message.client.json.v1.Status.DELIVERED) {
      pingEntity.setStatus(status);
      pingEntity.setStopTimeInMillis(Instant.now().toEpochMilli());
      pingEntity.setError("Sending status: " + status);
      logger.debug(Retention.SHORT, "Popping VaState for VPI: {} and correlationId: {} due to MtStatus {}", pingEntity.getVpi(), pingEntity.getCorrelationId(),
          mtStatus);

      return stateRepository.pop(pingEntity.getCorrelationId())
          .thenApply(states -> pingEntity)
          .toCompletableFuture();
    }

    return CompletableFuture.completedFuture(pingEntity);
  }

  private CompletableFuture<String> pingV1(RequestData requestData, String system) {
    UUID correlationId = UUID.randomUUID();
    logger.debug(Retention.SHORT, "CorrelationId: {} for REST PingV1, Vpi: {}", correlationId, requestData.vpi());

    return messageIdRepo.getNextMessageIdSequence()
        .thenCompose(swapCorrelationId -> createAndSaveEntity(requestData, correlationId, swapCorrelationId, 1)
            .thenCompose(v -> {
              if (StringUtils.hasLength(requestData.body())) {
                return sendMtBeefyStepV1.action(requestData, correlationId, swapCorrelationId, system);
              }
              return sendMtPingStepV1.action(requestData, correlationId, swapCorrelationId, system);
            })
            .exceptionallyCompose(
                throwable -> pingEntityService.findByMessageId(swapCorrelationId).thenCompose(pingEntity -> saveErrorToDB(pingEntity, throwable)))
            .thenApply(v -> correlationId.toString()));
  }

  private CompletableFuture<String> pingV2(RequestData requestData, String system) {
    UUID correlationId = UUID.randomUUID();

    return createAndSaveEntity(requestData, correlationId, null, 2)
        .thenCompose(v -> sendMtPingStepV2.action(requestData, correlationId, system))
        .exceptionallyCompose(throwable -> pingEntityService.findByCorrelationId(correlationId.toString()).thenCompose(e -> saveErrorToDB(e, throwable)))
        .thenApply(v -> correlationId.toString());
  }

  private CompletableFuture<Void> saveErrorToDB(PingEntity pingEntity, Throwable throwable) {
    if (pingEntity == null) {
      return CompletableFuture.completedFuture(null);
    }

    pingEntity.setError(throwable.getMessage());
    pingEntity.setStatus(Status.CANCELLED);
    return pingEntityService.upsert(pingEntity)
        .toCompletableFuture()
        .thenApply(savedPingEntity -> {
          logger.error("Could not Send Ping", throwable);
          throw new ResponseStatusException(HttpStatusCode.valueOf(500), "Could not send Ping", throwable);
        });
  }
}
