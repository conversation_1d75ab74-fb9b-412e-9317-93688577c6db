package com.volvo.tisp.vehiclepingservice.util;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import org.junit.jupiter.api.Assertions;

import com.volvo.tisp.vehiclepingservice.rest.model.Channel;

class ChannelUtilTest {
  @ParameterizedTest
  @CsvSource({
      "UDP, any",
      "UDP, UDP",
      "SAT, SATELLITE",
      "SMS, SMS",
  })
  void calculateResponseChannelTest(String channelValue, String receivingChannel) {
    Assertions.assertEquals(Channel.fromValue(channelValue), ChannelUtil.calculateResponseChannel(receivingChannel));
  }
}
