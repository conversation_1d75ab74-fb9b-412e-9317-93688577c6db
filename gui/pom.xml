<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.volvo.tisp.vehiclepingservice</groupId>
    <artifactId>vehicle-ping-service-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>vehicle-ping-service-server-gui</artifactId>
  <packaging>jar</packaging>
  <name>Vehicle Ping Service :: Server :: GUI</name>

  <dependencies>
    <dependency>
      <groupId>com.wirelesscar.config-lib</groupId>
      <artifactId>config-lib-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>

    <dependency>
      <groupId>com.wirelesscar.support-tool</groupId>
      <artifactId>support-tool-server-iwc</artifactId>
    </dependency>

  </dependencies>
</project>
