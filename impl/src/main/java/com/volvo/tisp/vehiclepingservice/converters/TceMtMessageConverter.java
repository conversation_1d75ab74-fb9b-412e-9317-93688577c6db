package com.volvo.tisp.vehiclepingservice.converters;

import static com.volvo.tisp.vehiclepingservice.jms.TceMessagePublisher.CLIENT_ID;
import static com.volvo.tisp.vehiclepingservice.jms.TceMessagePublisher.SERVICE_ID;

import java.util.Optional;
import java.util.UUID;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vehiclepingservice.jms.LegacyUriScheme;
import com.volvo.tisp.vehiclepingservice.jms.MtStatusJmsHandler;
import com.volvo.tisp.vehiclepingservice.rest.model.Channel;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SchedulerOption;
import com.wirelesscar.tce.api.v2.SrpOption;

public class TceMtMessageConverter {
  public static final int SERVICE_ID_INT = Integer.parseInt(SERVICE_ID);

  // TODO if all of the parameters are mandatory fields? If so, then we must validate all
  public static MtMessage createMtMessage(byte[] payload, Vpi vpi, int serviceVersion, String corrId, Channel channel, int timeout) {
  MtStatusReplyOption replyOption = new MtStatusReplyOption();
    replyOption.setCorrelationId(Optional.ofNullable(corrId).orElse(UUID.randomUUID().toString()));
    replyOption.setReplyDestination(
        LegacyUriScheme.createJMSDestinationName(MtStatusJmsHandler.MT_STATUS_IN_QUEUE));

    SchedulerOption schedulerOption = getSchedulerOption(channel, timeout);

    return getMtMessage(payload, vpi, serviceVersion, replyOption, schedulerOption);
  }

  private static MtMessage getMtMessage(byte[] payload, Vpi vpi, int serviceVersion, MtStatusReplyOption replyOption, SchedulerOption schedulerOption) {
    MtMessage mtMessage = new MtMessage();
    mtMessage.setPayload(payload);
    mtMessage.setVehiclePlatformId(vpi.toString());
    mtMessage.setMtStatusReplyOption(replyOption);
    SrpOption srpOption = new SrpOption();
    mtMessage.setSrpOption(srpOption);
    mtMessage.getSrpOption().setDstService(SERVICE_ID_INT);
    mtMessage.getSrpOption().setDstVersion(serviceVersion);
    mtMessage.getSrpOption().setPriority(4);
    mtMessage.setClientId(CLIENT_ID);
    mtMessage.setSchedulerOption(schedulerOption);
    return mtMessage;
  }

  private static SchedulerOption getSchedulerOption(Channel channel, int timeout) {
    SchedulerOption schedulerOption = new SchedulerOption();
    schedulerOption.setHint(getSendSchema(channel, timeout));
    schedulerOption.setPriority(4);
    schedulerOption.setEnqueueingType("OVERRIDE_IMMEDIATE");
    return schedulerOption;
  }

  private static String getSendSchema(Channel channel, int timeout) {
    if (channel == null || channel == Channel.UDP) {
      return timeout > 300 ? "low" : "dfol-mid-plus";
    }

    return switch (channel) {
      case SAT -> "sat-only";
      case SMS -> "sms-only";
      default -> "dfol-mid-plus";
    };
  }
}
