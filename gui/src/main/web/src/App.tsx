import * as React from "react";
import {
    STCAppBase,
    Tool
} from "support-tool-components";

import {
    faPowerOff,
    faTableTennis
} from "@fortawesome/free-solid-svg-icons";
import {VehiclePingTool} from "./tools/VehiclePingTool";
import {CustomPingTool} from "./tools/CustomPingTool";
import {BeefyTool} from "./tools/BeefyTool";
import {PermissionService} from "./services/PermissionService";
import {CustomBeefyTool} from "./tools/CustomBeefyTool";

interface AppProps {
}

interface AppState {
}

enum ToolKeys {
    VEHICLE_PING="vehicleping",
    VEHICLE_BEEFY="beefy",
    VEHICLE_PING_CUSTOM="custom",
    VEHICLE_BEEFY_CUSTOM="beefycustom"
}
export default class App extends React.Component<AppProps, AppState> {

    constructor(props:AppProps){
        super(props);
        this.state={
        }
    }
    private generateAboutInfo(): JSX.Element {
        return (
            <div>
                <p>Vehicle Ping Service supports sending a UDP ping-pong message to test communication</p>
            </div>
        )
    }

    private generateToolsList(): Tool[] {
        // Add your tools here
        let tools = [            {
            name: "Vehicle Ping Tool",
            key: ToolKeys.VEHICLE_PING,
            description: "This is a tool for pinging vehicles.",
            //group: any, //You can use this to group tools in the menu.
            component: <VehiclePingTool/>
        }]

        let permission = new PermissionService()
        let beefy = permission.isAuthenticatedForBeefy()
        let custom = permission.isAuthenticatedForCustom()

        if (beefy == true) {
            tools.push(
                {
                    name: "Beefy Message Tool",
                    key: ToolKeys.VEHICLE_BEEFY,
                    description: "This is a tool for pinging vehicles with Beefy Messages.",
                    //group: any, //You can use this to group tools in the menu.
                    component: <BeefyTool/>
                }
            )
        }

        if (custom == true) {
            tools.push(
                {
                    name: "Custom Ping Tool",
                    key: ToolKeys.VEHICLE_PING_CUSTOM,
                    description: "This is a tool for pinging vehicles with alternative to TCE.",
                    //group: any, //You can use this to group tools in the menu.
                    component: <CustomPingTool/>
                }
            )
        }

        if (beefy == true && custom == true) {
            tools.push(
                {
                    name: "Custom Beefy Message Tool",
                    key: ToolKeys.VEHICLE_BEEFY_CUSTOM,
                    description: "This is a tool for pinging vehicles with Beefy Messages with alternative to TCE.",
                    //group: any, //You can use this to group tools in the menu.
                    component: <CustomBeefyTool />
                }
            )
        }

        return tools;
    }

    render(): React.ReactNode {
        return (
            <STCAppBase
                appName={"Vehicle Ping Service"}
                appIcon={faTableTennis}
                devModeToolRoutingKey={ToolKeys.VEHICLE_PING}
                appDescriptionPanel={this.generateAboutInfo()}
                documentationLocation={"https://vgcs-confluence.it.volvo.net/display/VEA/Platform+Component+-+Vehicle+Ping+Service"}
                teamName={"Vehicle Communication"}
                supportEmail={"<EMAIL>"}
                teamIcon={faPowerOff}
                tools={this.generateToolsList()}
            />
        );
    }


}