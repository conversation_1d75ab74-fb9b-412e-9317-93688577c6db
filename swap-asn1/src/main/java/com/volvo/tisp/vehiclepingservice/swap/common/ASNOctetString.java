package com.volvo.tisp.vehiclepingservice.swap.common;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.Arrays;

import org.jdom2.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.lang.SuppressForbidden;

/**
 * Title: ASN Octet String base class.
 *
 * <p>Description: Part of ASN
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR>
 * @version 1.0
 */
@SuppressForbidden
public class ASNOctetString extends ASNConstrainedValue {
  // table to convert a nibble to a hex char.
  private static final char[] HEX_CHAR = {
      '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'
  };
  private static final Logger logger = LoggerFactory.getLogger(ASNOctetString.class);
  private static final long serialVersionUID = 1L;
  protected ASNInteger m_iReferenceSize = null;
  protected long size = 0;
  protected byte[] value = null;

  public ASNOctetString() {
    this("OctetString");
  }

  public ASNOctetString(String name) {
    // If any constraints apply it will be set after constructor.
    constrainType = UNBOUNDED_COUNT;
    setAsnObjectName(name);
  }

  /**
   * Convert a hex string to a byte array. Permits upper or lower case hex.
   *
   * @param s String must have even number of characters. and be formed only of digits 0-9 A-F or
   *          a-f. No spaces, minus or plus signs.
   * @return corresponding byte array.
   */
  public static byte[] fromHexString(String s) {
    int stringLength = s.length();
    if ((stringLength & 0x1) != 0) {
      throw new IllegalArgumentException("fromHexString requires an even number of hex characters");
    }
    byte[] b = new byte[stringLength / 2];
    for (int i = 0, j = 0; i < stringLength; i += 2, j++) {
      int high = charToNibble(s.charAt(i));
      int low = charToNibble(s.charAt(i + 1));
      b[j] = (byte) ((high << 4) | low);
    }

    return b;
  }

  public static String toHexString(byte[] b) {
    StringBuffer sb = new StringBuffer(b.length * 2);
    for (int i = 0; i < b.length; i++) {
      // look up high nibble char
      sb.append(HEX_CHAR[(b[i] & 0xf0) >>> 4]);

      // look up low nibble char
      sb.append(HEX_CHAR[b[i] & 0x0f]);
    }
    return sb.toString();
  }

  /**
   * convert a single char to corresponding nibble.
   *
   * @param c char to convert. must be 0-9 a-f A-F, no spaces, plus or minus signs.
   * @return corresponding integer
   */
  private static int charToNibble(char c) {
    if ('0' <= c && c <= '9') {
      return c - '0';
    } else if ('a' <= c && c <= 'f') {
      return c - 'a' + 0xa;
    } else if ('A' <= c && c <= 'F') {
      return c - 'A' + 0xa;
    } else {
      throw new IllegalArgumentException("Invalid hex character: " + c);
    }
  }

  public ASNValue createObject() throws ASNException {
    return new ASNOctetString();
  }

  public void decode(PERStream stream) throws ASNException {
    if (stream.isDebug()) {
      logger.debug("S " + stream.position() + " " + getAsnObjectName() + "\tASNOctetString");
    }

    long tmpSize = 0;
    int fragmentLen = 0;
    byte[] tmpVal = null;
    do {
      fragmentLen = (int) decodeLength(stream);
      tmpVal = decodeValue(stream, fragmentLen, tmpVal);
      tmpSize += fragmentLen;
    } while (fragmentLen >= 16384);

    size = tmpSize;
    value = tmpVal;

    if (stream.isDebug()) {
      logger.debug("E " + stream.position() + " " + getAsnObjectName() + "\tASNOctetString");
    }
  }

  public void decode(Element oElement) throws ASNException {
    String elementText = oElement.getTextTrim();

    if (elementText.startsWith("0x"))
      set(fromHexString(elementText.substring(2)));
    else
      set(oElement.getTextTrim());
  }

  public void encode(PERStream stream) throws ASNException {
    if (value == null) {
      throw new ASNException("Cannot encode null value");
    }
    long bytesLeft = size;
    while (bytesLeft >= 0) {
      long bytesToEncodeThisPass = encodeLength(stream, bytesLeft);
      if (bytesToEncodeThisPass > 0) {
        encodeValue(stream, (int) (size - bytesLeft), bytesToEncodeThisPass);
        bytesLeft -= bytesToEncodeThisPass;
        if (bytesLeft == 0 && bytesToEncodeThisPass < 16384) {
          bytesLeft = -1;
        }
      } else {
        bytesLeft = -1;
      }
    }
  }

  public void encode(Element oElement) throws ASNException {
    boolean bPrintable = true;
    Element oElem = new Element(getAsnObjectName());
    for (int i = 0; i < value.length; i++) {
      if (Character.isISOControl((char) value[i])) {
        bPrintable = false;
      }
    }
    if (bPrintable) {
      oElem.addContent(getAsString());
    } else {
      oElem.addContent("0x" + toHexString(value));
    }
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long retVal = super.encodedSize();
    if (constrainType == UNBOUNDED_COUNT) {
      try {
        retVal += PERStream.getLengthDeterminatorSizeInBits(value.length);
      } catch (ASNException e) {
        throw new RuntimeException(e.getMessage(), e);
      }
    } else if (constrainType != FIXED_SIZE) {
      retVal += countBits(lowBoundary, highBoundary);
    }
    retVal += getEncodedItemSizeInBits() * size;
    return retVal;
  }

  public String get(String charSetName) throws UnsupportedEncodingException {
    int length = value.length;

    for (int i = value.length - 1; i >= 0; i--) {
      if (value[i] != (byte) 0) {
        break;
      }
      length--;
    }
    return new String(value, 0, length, charSetName);
  }

  public byte[] getAsByteArray() {
    return Arrays.copyOf(value, value.length);
  }

  public String getAsString() {
    int start = 0;
    int length = value.length;

    for (int i = value.length - 1; i >= 0; i--) {
      if (value[i] != (byte) 0) {
        break;
      }
      length--;
    }
    return new String(value, start, length, Charset.defaultCharset());
  }

  public long getSize() {
    return size;
  }

  public String getValueAsString() {
    return " " + value.length + " octets { " + toHexString(value) + " }";
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    size = in.readLong();
    value = new byte[(int) size];
    for (int i = 0; i < size; i++) {
      value[i] = in.readByte();
    }
  }

  public void set(String val) {
    this.value = val.getBytes(Charset.defaultCharset());
    if (constrainType != FIXED_SIZE) {
      size = value.length;
      if (m_iReferenceSize != null) {
        m_iReferenceSize.set(size);
      }
    }
  }

  public void set(String val, String charSetName) throws UnsupportedEncodingException {
    this.value = val.getBytes(charSetName);
    if (constrainType != FIXED_SIZE) {
      size = value.length;
      if (m_iReferenceSize != null) {
        m_iReferenceSize.set(size);
      }
    }
  }

  public void set(byte[] val) {
    if (constrainType != FIXED_SIZE) {
      size = val.length;
    }
    if (m_iReferenceSize != null) {
      m_iReferenceSize.set(size);
    }
    this.value = Arrays.copyOf(val, val.length);
  }

  public void setFixedConstraint(long highBoundary) {
    size = highBoundary;
    super.setFixedConstraint(highBoundary);
  }

  public void setFixedSizeConstraint(int highBoundary) {
    size = highBoundary;
    super.setFixedSizeConstraint(highBoundary);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    out.writeLong(size);
    for (int i = 0; i < value.length; i++) {
      out.writeByte(value[i]);
    }
    // Fill the rest with zero, if there is a difference between the fixed
    // length and the actual buffer
    for (int i = value.length; i < size; i++) {
      out.writeByte((byte) 0);
    }
  }

  protected long decodeLength(PERStream stream) throws ASNException {
    long retVal = -1;
    if (constrainType == UNBOUNDED_COUNT) {
      retVal = stream.decodeLengthDeterminant();
    } else if (constrainType != FIXED_SIZE && m_iReferenceSize == null) {
      retVal = lowBoundary + stream.decodeInteger(countBits(lowBoundary, highBoundary));
    } else if (constrainType == FIXED_SIZE) {
      retVal = size;
    }

    if (m_iReferenceSize != null) {
      retVal = m_iReferenceSize.get();
    }
    return retVal;
  }

  protected byte[] decodeValue(PERStream stream, int fragmentLen, byte[] currentBuf)
      throws ASNException {
    int newSize = fragmentLen;
    int offset = 0;
    if (currentBuf != null) {
      newSize += currentBuf.length;
      offset = currentBuf.length;
    }
    byte[] newVal = new byte[newSize];
    if (currentBuf != null) {
      System.arraycopy(currentBuf, 0, newVal, 0, currentBuf.length);
    }
    for (int i = 0; i < fragmentLen; i++) {
      newVal[offset + i] = stream.decodeByte();
    }
    return newVal;
  }

  // Fast convert a byte array to a hex string
  // with possible leading zero.

  protected long encodeLength(PERStream stream, long bytesLeft) throws ASNException {
    if (constrainType == UNBOUNDED_COUNT) {
      return stream.encodeLengthDeterminant(bytesLeft);
    } else if (constrainType != FIXED_SIZE && m_iReferenceSize == null) {
      // Can't use size when not constrainType FIXED_SIZE since it is
      // overwritten with value.length on setValue, use highBoundary instead
      if (value.length > highBoundary || value.length < lowBoundary) {
        throw new ASNException(
            "OctetString is out of range. Expected ("
                + lowBoundary
                + ".."
                + highBoundary
                + ") octets but was "
                + value.length);
      }
      stream.encodeInteger(size - lowBoundary, countBits(lowBoundary, highBoundary));
      return size;
    } else {
      // Fixed size constraint
      if (value.length != size) {
        throw new ASNException(
            "OctetString is out of range. Expected " + size + " octets but was " + value.length);
      }
      return size;
    }
  }

  protected void encodeValue(PERStream stream, int startAtIndex, long byteCount)
      throws ASNException {
    for (int i = 0; i < byteCount; i++) {
      stream.encodeByte(value[startAtIndex + i]);
    }
  }

  protected int getEncodedItemSizeInBits() {
    return 8;
  }

  void setReferenceSize(ASNInteger refSize) {
    constrainType = REFERENCE_SIZE;
    m_iReferenceSize = refSize;
  }
}
